# Flight Search API Integration

This document explains the flight search API integration implementation in the Flight B2C application.

## Overview

The flight search functionality has been integrated with a backend API that accepts flight search requests and returns flight results. The system automatically determines whether a flight is domestic or international based on the selected airports' countries.

## Key Components

### 1. Flight Service (`app/api/services/flightService.ts`)

The main service file that handles all flight-related API operations.

#### Key Interfaces:

- `FlightSearchRequest`: The request format expected by the backend API
- `FlightSearchResponse`: The response format from the backend API
- `FlightSearchFormData`: Frontend form data structure
- `Flight`: Individual flight data structure

#### Key Functions:

- `flightService.searchFlights()`: Direct API call with request object
- `flightService.searchFlightsWithFormData()`: Convenience method that accepts form data
- `isFlightDomestic()`: Determines if flight is domestic or international
- `transformToFlightSearchRequest()`: Converts form data to API request format

### 2. API Request Format

The backend expects the following request format:

```json
{
  "ADT": 1,
  "CHD": 0,
  "INF": 0,
  "Cabin": "E",
  "Source": "CF",
  "Mode": "AS",
  "ClientID": "",
  "FareType": "ON",
  "SecType": "I",
  "TUI": "",
  "Trips": [
    {
      "From": "BOM",
      "To": "DXB",
      "OnwardDate": "2025-08-13",
      "ReturnDate": "",
      "TUI": ""
    }
  ],
  "Parameters": {
    "IsDirect": false,
    "PaxCategory": "NA",
    "Refundable": ""
  }
}
```

#### Field Descriptions:

- `ADT`: Number of adults
- `CHD`: Number of children
- `INF`: Number of infants
- `Cabin`: Cabin class (E=Economy, PE=Premium Economy, B=Business, F=First)
- `Source`: Source identifier (fixed as "CF")
- `Mode`: Mode identifier (fixed as "AS")
- `ClientID`: Client identifier (can be empty)
- `FareType`: Fare type (ON=Online, OFF=Offline)
- `SecType`: Sector type (D=Domestic, I=International) - **automatically determined**
- `TUI`: TUI identifier (can be empty)
- `Trips`: Array of trip segments
- `Parameters`: Additional search parameters

### 3. Domestic vs International Detection

The system automatically determines flight type by:

1. Getting airport data for both origin and destination
2. Comparing the `country` field of both airports
3. Setting `SecType` to "D" for domestic (same country) or "I" for international (different countries)

### 4. Frontend Integration

#### FlightSearch Component (`components/FlightSearch.tsx`)

Updated to pass airport codes in search parameters:

```javascript
const searchParams = new URLSearchParams({
  from: selectedFromAirport.code, // Airport code (e.g., "BOM")
  to: selectedToAirport.code,     // Airport code (e.g., "DXB")
  fromName: selectedFromAirport.name, // Display name
  toName: selectedToAirport.name,     // Display name
  // ... other parameters
});
```

#### Flights Page (`app/flights/page.tsx`)

Updated to use the flight service:

1. Parses search parameters
2. Gets airport data using codes
3. Calls flight search API
4. Handles loading and error states
5. Falls back to mock data if API fails

## Usage Examples

### Basic Flight Search

```typescript
import { flightService, FlightSearchFormData } from '@/app/api/services/flightService';

const searchData: FlightSearchFormData = {
  from: { code: 'BOM', name: 'Mumbai', fullName: 'Mumbai Airport', country: 'India' },
  to: { code: 'DXB', name: 'Dubai', fullName: 'Dubai Airport', country: 'UAE' },
  departureDate: '2025-08-13',
  adults: 1,
  children: 0,
  infants: 0,
  cabin: 'E',
  tripType: 'oneWay',
  fareType: 'ON',
  isDirect: false
};

try {
  const results = await flightService.searchFlightsWithFormData(searchData);
  console.log('Flight results:', results);
} catch (error) {
  console.error('Search failed:', error);
}
```

### Check if Flight is Domestic

```typescript
import { isFlightDomestic } from '@/app/api/services/flightService';

const isDomestic = await isFlightDomestic('DEL', 'BOM'); // true (both in India)
const isInternational = await isFlightDomestic('BOM', 'DXB'); // false (India to UAE)
```

## Error Handling

The system includes comprehensive error handling:

1. **API Errors**: Network or server errors are caught and displayed
2. **Airport Not Found**: Invalid airport codes are handled gracefully
3. **Fallback Data**: Mock data is used if API fails (for development)
4. **Loading States**: Proper loading indicators during API calls

## Testing

Unit tests are available in `app/api/services/__tests__/flightService.test.ts` covering:

- Domestic vs international flight detection
- Form data transformation
- Error handling scenarios

## Configuration

The flight search API is configured with a separate axios instance in `app/api/services/flightService.ts`:

```typescript
const flightApi = axios.create({
  baseURL: 'http://*************:8080',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout for flight searches
});
```

**API Endpoint**: `/search` (POST request)
**Full URL**: `http://*************:8080/search`

## Next Steps

1. **API Endpoint**: Ensure the backend API endpoint `/search` is implemented at `http://*************:8080`
2. **Response Mapping**: Update response transformation if backend format differs
3. **Caching**: Consider implementing flight search result caching
4. **Filters**: Integrate advanced filtering with API parameters
5. **Real-time Updates**: Add flight price/availability monitoring
