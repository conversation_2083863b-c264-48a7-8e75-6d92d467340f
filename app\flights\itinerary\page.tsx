'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getAirlineLogo, getAirlineName, getDisplayFlightNumber } from '../../utils/airlineUtils';

export default function ItineraryPage() {
  const [bookingData, setBookingData] = useState(null);

  // Mock booking data - normally would come from URL params or API
  const mockBookingData = {
    bookingId: 'NF2AA1SZ91303338362',
    pnr: 'I4G3UA',
    bookingDate: '16 Sep 2024',
    status: 'Completed',
    flight: {
      route: 'Coimbatore → Delhi',
      airline: '6E',
      logo: '/AirlineLogo/6E.png',
      flightNumber: '6E-2409',
      departure: {
        time: '21:15',
        date: 'Tue, 17 Sep',
        airport: 'CJB',
        city: 'Coimbatore',
        fullName: 'Coimbatore Airport',
        duration: '3h 5m'
      },
      arrival: {
        time: '00:20',
        date: 'Wed, 18 Sep',
        airport: 'DEL',
        city: 'Delhi',
        fullName: 'Indira Gandhi International Airport',
        terminal: 'Terminal 2'
      },
      stops: 'Non-stop',
      baggage: {
        cabin: '7 Kgs (1 piece only)/ Adult',
        checkin: '15 Kgs (1 piece only)/ Adult'
      },
      fareType: {
        economy: 'Economy',
        regular: 'Regular Fare',
        saver: 'SAVER'
      }
    },
    passengers: [
      {
        id: 1,
        name: 'ABDUL RASEEM MK',
        pnrTicketNo: 'I4G3UA',
        gender: 'Male, Adult',
        seat: '-',
        meal: '-',
        excessBaggage: '-'
      },
      {
        id: 2,
        name: 'ABDUL NAZAR PK',
        pnrTicketNo: 'I4G3UA',
        gender: 'Male, Adult',
        seat: '-',
        meal: '-',
        excessBaggage: '-'
      }
    ],
    contact: {
      name: 'ABDUL RASEEM MK',
      email: '<EMAIL>',
      phone: '919526096069'
    },
    pricing: {
      basefare: 18400,
      taxes: 2900,
      convenienceFee: 730,
      totalPaid: 22030
    },
    travelers: '2 Traveller(s)',
    journey: 'Hope you had a nice journey!'
  };

  useEffect(() => {
    setBookingData(mockBookingData);
  }, []);

  const downloadInvoice = () => {
    console.log('Downloading invoice...');
    // Handle invoice download
  };

  if (!bookingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-[#013688] border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading itinerary...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="pt-20 pb-16 px-4">
        <div className="max-w-7xl mx-auto">
          {/* Header Section */}
          <div className="bg-gradient-to-r from-secondary to-secondary/70 rounded-lg shadow-lg p-8 mb-8 text-white relative overflow-hidden">
            
            <div className="relative z-10">
              <h1 className="text-3xl font-bold mb-2">{bookingData.journey}</h1>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-lg font-medium">Booking ID {bookingData.bookingId}</p>
                  <p className="text-teal-100">Booked on {bookingData.bookingDate}</p>
                </div>
                <div className="text-right">
                  <div className="bg-white/20 rounded-lg p-3">
                    <div className="text-sm font-medium">Download invoice(s)</div>
                    <button 
                      onClick={downloadInvoice}
                      className="flex items-center space-x-2 text-white hover:text-teal-100 transition-colors mt-1"
                    >
                      <i className="ri-download-line"></i>
                      <span className="text-sm underline">Download</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Flight Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                {/* Flight Header */}
                <div className="border-b border-gray-100 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-bold text-gray-900 flex items-center space-x-2">
                      <i className="ri-plane-line text-[#013688]"></i>
                      <span>{bookingData.flight.route}</span>
                    </h2>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{bookingData.travelers}</span>
                      <span>•</span>
                      <span>{bookingData.flight.stops}</span>
                      <button className="text-[#013688] hover:text-blue-700 flex items-center space-x-1">
                        <i className="ri-arrow-up-line transform rotate-45"></i>
                      </button>
                    </div>
                  </div>

                  {/* Flight Status and Info */}
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="flex items-center space-x-2">
                      <img src={getAirlineLogo(bookingData.flight.airline, bookingData.flight.logo)} alt={getAirlineName(bookingData.flight.airline)} className="w-12 h-8 object-contain" />
                      <div>
                        <div className="font-semibold text-gray-900">{getAirlineName(bookingData.flight.airline)}</div>
                        <div className="text-sm text-gray-500">{getDisplayFlightNumber(bookingData.flight.airline, bookingData.flight.flightNumber)}</div>
                      </div>
                    </div>
                    <div className="bg-green-100 text-green-800 px-3 py-1 rounded text-xs font-bold">
                      {bookingData.status}
                    </div>
                  </div>

                  {/* Flight Route */}
                  <div className="mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-gray-900">{bookingData.flight.departure.time}</div>
                        <div className="text-sm font-medium text-gray-700">{bookingData.flight.departure.date}</div>
                        <div className="text-sm text-gray-600">{bookingData.flight.departure.city} - {bookingData.flight.departure.airport}</div>
                        <div className="text-xs text-gray-500">{bookingData.flight.departure.fullName}</div>
                      </div>
                      
                      <div className="flex-1 px-6">
                        <div className="text-center mb-2">
                          <span className="text-sm text-gray-600">{bookingData.flight.departure.duration}</span>
                        </div>
                        <div className="relative">
                          <div className="h-0.5 bg-gray-300 w-full"></div>
                          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-2">
                            <i className="ri-plane-line text-[#013688] text-lg"></i>
                          </div>
                        </div>
                        <div className="text-center mt-2">
                          <span className="text-xs text-green-600 font-medium">{bookingData.flight.stops}</span>
                        </div>
                      </div>
                      
                      <div className="text-center">
                        <div className="text-3xl font-bold text-gray-900">{bookingData.flight.arrival.time}</div>
                        <div className="text-sm font-medium text-gray-700">{bookingData.flight.arrival.date}</div>
                        <div className="text-sm text-gray-600">{bookingData.flight.arrival.city} - {bookingData.flight.arrival.airport}</div>
                        <div className="text-xs text-gray-500">{bookingData.flight.arrival.fullName}</div>
                        <div className="text-xs text-gray-500">{bookingData.flight.arrival.terminal}</div>
                      </div>
                    </div>
                  </div>

                  {/* Baggage Allowance */}
                  <div className="bg-gray-50 rounded-lg p-4">
                    <h4 className="font-semibold text-gray-900 mb-3">Baggage allowance</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <i className="ri-suitcase-line text-gray-600"></i>
                        <div>
                          <div className="font-medium">Cabin Bag:</div>
                          <div className="text-gray-600">{bookingData.flight.baggage.cabin}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <i className="ri-luggage-cart-line text-gray-600"></i>
                        <div>
                          <div className="font-medium">Check-in Bag:</div>
                          <div className="text-gray-600">{bookingData.flight.baggage.checkin}</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Fare Type */}
                  <div className="mt-4 flex items-center space-x-6 text-sm">
                    <div className="flex items-center space-x-2">
                      <i className="ri-price-tag-3-line text-gray-600"></i>
                      <span className="text-gray-600">Fare type:</span>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center space-x-1">
                        <i className="ri-checkbox-circle-fill text-green-600 text-xs"></i>
                        <span>{bookingData.flight.fareType.economy}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <i className="ri-checkbox-circle-fill text-green-600 text-xs"></i>
                        <span>{bookingData.flight.fareType.regular}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <i className="ri-checkbox-circle-fill text-green-600 text-xs"></i>
                        <span>{bookingData.flight.fareType.saver}</span>
                      </span>
                    </div>
                  </div>
                </div>

                {/* Traveller Details Table */}
                <div className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-4">Traveller Details</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="bg-gray-50 border-b border-gray-200">
                          <th className="text-left py-3 px-4 font-medium text-gray-700">Traveller Details</th>
                          <th className="text-left py-3 px-4 font-medium text-gray-700">PNR/E-Ticket No.</th>
                          <th className="text-left py-3 px-4 font-medium text-gray-700">SEAT</th>
                          <th className="text-left py-3 px-4 font-medium text-gray-700">MEAL</th>
                          <th className="text-left py-3 px-4 font-medium text-gray-700">EXCESS BAGGAGE</th>
                        </tr>
                      </thead>
                      <tbody>
                        {bookingData.passengers.map((passenger, index) => (
                          <tr key={passenger.id} className="border-b border-gray-100">
                            <td className="py-4 px-4">
                              <div className="flex items-center space-x-3">
                                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                  <i className="ri-user-line text-gray-600"></i>
                                </div>
                                <div>
                                  <div className="font-semibold text-gray-900">{passenger.name}</div>
                                  <div className="text-xs text-gray-500">{passenger.gender}</div>
                                </div>
                              </div>
                            </td>
                            <td className="py-4 px-4 font-semibold text-[#013688]">{passenger.pnrTicketNo}</td>
                            <td className="py-4 px-4 text-gray-600">{passenger.seat}</td>
                            <td className="py-4 px-4 text-gray-600">{passenger.meal}</td>
                            <td className="py-4 px-4 text-gray-600">{passenger.excessBaggage}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              {/* Important Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="font-semibold text-gray-900 mb-4">IMPORTANT INFORMATION</h3>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Unaccompanied Minors Travelling:</h4>
                    <ul className="text-sm text-gray-600 space-y-1 ml-4">
                      <li>• Any passenger who refers to a child traveling without an adult aged 18 or older. Please check with the airline for their rules and regulations regarding unaccompanied minors, as these can differ between airlines.</li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Check travel guidelines and baggage information below:</h4>
                    <ul className="text-sm text-gray-600 space-y-1 ml-4">
                      <li>• Carry no more than 1 check-in baggage and 1 hand baggage per passenger. If violated, airline may levy extra charges.</li>
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-2">Valid ID proof needed</h4>
                    <ul className="text-sm text-gray-600 space-y-1 ml-4">
                      <li>• Carry a valid photo identification proof (Driver Licence, Aadhar Card, Pan Card or any other Government recognised photo identification)</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Primary Contact Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="font-semibold text-gray-900 mb-4">PRIMARY CONTACT DETAILS</h3>
                <p className="text-sm text-gray-600 mb-4">This is your primary contact, you cannot change it. you can however send the ticket to other emails</p>
                
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <i className="ri-user-line text-gray-600"></i>
                    </div>
                    <span className="font-medium text-gray-900">{bookingData.contact.name}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <i className="ri-mail-line text-gray-600"></i>
                    </div>
                    <span className="text-gray-700">{bookingData.contact.email}</span>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                      <i className="ri-phone-line text-gray-600"></i>
                    </div>
                    <span className="text-gray-700">{bookingData.contact.phone}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Sidebar */}
            <div className="lg:col-span-1 space-y-6">
              {/* Ticket Actions */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Ticket(s)</h3>
                
                <button 
                  onClick={downloadInvoice}
                  className="w-full bg-[#013688] text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 mb-3"
                >
                  <i className="ri-download-line"></i>
                  <span>Download invoice(s)</span>
                </button>
                
                <Link href="/flights/booking/success">
                  <button className="w-full bg-gray-100 text-gray-700 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors">
                    Back to Booking
                  </button>
                </Link>
              </div>

              {/* Airline Contact */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Airline Contact</h3>
                
                <div className="flex items-center space-x-3">
                  <img src={getAirlineLogo(bookingData.flight.airline, bookingData.flight.logo)} alt={getAirlineName(bookingData.flight.airline)} className="w-12 h-8 object-contain" />
                  <div>
                    <div className="font-medium text-gray-900">{getAirlineName(bookingData.flight.airline)}</div>
                    <div className="text-sm text-gray-600">0124-6173838, 0124-4973838</div>
                  </div>
                </div>
              </div>

              {/* Price Breakup */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="font-semibold text-gray-900 mb-4">Price Breakup</h3>
                
                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Airline Base Fare</span>
                    <span className="font-medium">₹ {bookingData.pricing.basefare.toLocaleString()}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">Airline Taxes</span>
                    <span className="font-medium">₹ {bookingData.pricing.taxes.toLocaleString()}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">Convenience Fee</span>
                    <span className="font-medium">₹ {bookingData.pricing.convenienceFee}</span>
                  </div>
                  
                  <div className="border-t border-gray-200 pt-3 flex justify-between">
                    <span className="font-semibold text-gray-900">Amount Paid</span>
                    <span className="font-bold text-lg text-[#013688]">₹ {bookingData.pricing.totalPaid.toLocaleString()}</span>
                  </div>
                </div>
              </div>

              {/* Forex Card Promotion */}
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-6">
                <h3 className="font-semibold text-gray-900 mb-3">Forex: Delivered To Your Doorstep</h3>
                
                <p className="text-sm text-gray-700 mb-4">
                  Zero markup forex card or currency delivered by RBI authorized agencies for your upcoming trip.
                </p>
                
                <div className="flex items-center justify-between">
                  <button className="bg-[#013688] text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
                    GET FOREX NOW
                  </button>
                  <div className="flex items-center space-x-1 text-xs text-gray-600">
                    
                    <span>A Unimoni Group Company</span>
                  </div>
                </div>
              </div>

              {/* Advertisement */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div className="p-4">
                  <div className="text-xs text-gray-500 text-right mb-2">Sponsored</div>
                  <div className="flex items-start space-x-3">
                    <img 
                      src="https://readdy.ai/api/search-image?query=mountain%20travel%20adventure%20photography%20smartphone%20oppo%20camera%20promotion&width=80&height=80&seq=oppo-promo&orientation=squarish" 
                      alt="Oppo Campaign"
                      className="w-20 h-20 object-cover rounded-lg"
                    />
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 text-sm mb-2">
                        From City Lights to Mountain Heights— Capture the Best Moments of Your Dream Trip with #OPPOReno14Series.
                      </h4>
                      <button className="bg-gray-900 text-white px-4 py-2 rounded text-xs font-medium hover:bg-gray-800 transition-colors">
                        KNOW MORE
                      </button>
                    </div>
                    <img 
                      src="https://readdy.ai/api/search-image?query=Oppo%20brand%20logo%20smartphone%20technology&width=40&height=20&seq=oppo-logo&orientation=landscape" 
                      alt="Oppo Logo"
                      className="h-5 object-contain"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
}