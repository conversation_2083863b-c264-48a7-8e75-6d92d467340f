'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, AuthContextType, OTPResponse, AuthResponse, LoginApiRequest, RefreshTokenApiResponse } from '../types/auth';
import { authService } from '../api/services/authService';
import { storageUtils } from '../api/utils/storageUtils';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize auth state from localStorage on mount
  useEffect(() => {
    const initializeAuth = () => {
      try {
        const storedUser = storageUtils.getUser();
        const isAuthenticated = storageUtils.isAuthenticated();

        if (storedUser && isAuthenticated) {
          // Normalize user data to match interface
          const normalizedUser: User = {
            id: storedUser.id,
            name: storedUser.name,
            email: storedUser.email,
            phone: storedUser.phone,
            profilePicture: storedUser.profilePicture || storedUser.profile_picture,
            isVerified: storedUser.isVerified ?? storedUser.is_verified ?? false,
            createdAt: storedUser.createdAt || storedUser.created_at || new Date().toISOString(),
            lastLogin: storedUser.lastLogin || storedUser.last_login || new Date().toISOString()
          };
          setUser(normalizedUser);
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        // Clear invalid data
        storageUtils.clearAuthData();
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (phoneOrEmail: string): Promise<OTPResponse> => {
    try {
      setIsLoading(true);

      // Determine if input is email or phone
      const isEmail = phoneOrEmail.includes('@');
      const otpRequest = {
        [isEmail ? 'email' : 'phone']: phoneOrEmail
      };

      // Call the new API
      const response = await authService.sendOTP(otpRequest);

      // Store the phone/email temporarily for OTP verification
      if (response.success) {
        localStorage.setItem('temp_login_identifier', phoneOrEmail);
      }

      // Return response directly as it already matches the expected interface
      return response;
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'Failed to send OTP. Please try again.'
      };
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOTP = async (otpId: string, otp: string): Promise<AuthResponse> => {
    try {
      setIsLoading(true);

      // For the new API, we need the original phone/email, not the otpId
      // We'll need to store the phoneOrEmail from the login step
      // For now, we'll extract it from localStorage or use a different approach
      const storedPhoneOrEmail = localStorage.getItem('temp_login_identifier');

      if (!storedPhoneOrEmail) {
        return {
          success: false,
          message: 'Login session expired. Please start over.'
        };
      }

      // Call the new API for OTP verification
      const response = await authService.verifyOTP(storedPhoneOrEmail, otp);

      if (response.success && response.user) {
        setUser(response.user);

        // Clear temporary storage
        localStorage.removeItem('temp_login_identifier');

        return response;
      } else {
        return {
          success: false,
          message: response.message
        };
      }
    } catch (error) {
      console.error('OTP verification error:', error);
      return {
        success: false,
        message: 'Verification failed. Please try again.'
      };
    } finally {
      setIsLoading(false);
    }
  };

  const socialLogin = async (provider: string): Promise<AuthResponse> => {
    try {
      setIsLoading(true);
      
      // Simulate social login API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock successful social login
      const mockUser: User = {
        id: `user_${provider}_${Date.now()}`,
        name: `${provider} User`,
        email: `user@${provider}.com`,
        profilePicture: `https://via.placeholder.com/100?text=${provider[0].toUpperCase()}`,
        isVerified: true,
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
      };
      
      // Store user data and token
      localStorage.setItem('ecogo_user', JSON.stringify(mockUser));
      localStorage.setItem('ecogo_token', `${provider}_token_${Date.now()}`);
      
      setUser(mockUser);
      
      return {
        success: true,
        message: `${provider} login successful`,
        user: mockUser,
        token: `${provider}_token_${Date.now()}`
      };
    } catch (error) {
      console.error('Social login error:', error);
      return {
        success: false,
        message: `${provider} login failed. Please try again.`
      };
    } finally {
      setIsLoading(false);
    }
  };

  const loginWithCredentials = async (request: LoginApiRequest): Promise<AuthResponse> => {
    try {
      setIsLoading(true);

      // Use verifyOTP method instead of login
      const phoneOrEmail = request.email || request.phone || '';
      const response = await authService.verifyOTP(phoneOrEmail, request.otp || '');

      if (response.success && response.data) {
        // Normalize user data
        const normalizedUser: User = {
          id: response.data.user.id,
          name: response.data.user.name,
          email: response.data.user.email,
          phone: response.data.user.phone,
          profilePicture: response.data.user.profile_picture,
          isVerified: response.data.user.is_verified,
          createdAt: response.data.user.created_at,
          lastLogin: response.data.user.last_login
        };

        setUser(normalizedUser);

        return {
          success: true,
          message: response.message,
          user: normalizedUser,
          token: response.data.access_token,
          data: response.data
        };
      } else {
        return {
          success: false,
          message: response.message || 'Login failed. Please try again.',
          error: response.error
        };
      }
    } catch (error) {
      console.error('Login with credentials error:', error);
      return {
        success: false,
        message: 'Login failed. Please try again.'
      };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);

      // Call the API logout endpoint
      await authService.logout();

      // Clear local state
      setUser(null);
    } catch (error) {
      console.error('Logout error:', error);
      // Clear local state even if API call fails
      setUser(null);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshToken = async (): Promise<RefreshTokenApiResponse> => {
    try {
      setIsLoading(true);

      const response = await authService.refreshToken();

      return response;
    } catch (error) {
      console.error('Refresh token error:', error);
      return {
        success: false,
        message: 'Token refresh failed'
      };
    } finally {
      setIsLoading(false);
    }
  };

  const updateUser = (userData: Partial<User>) => {
    if (user) {
      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      storageUtils.setUser(updatedUser);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    loginWithCredentials,
    verifyOTP,
    socialLogin,
    logout,
    refreshToken,
    updateUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
