# Technical Context

## Technology Stack
- **Framework**: Next.js 15.1.3 (App Router)
- **Frontend**: React 19.0.0, TypeScript 5
- **Styling**: Tailwind CSS 3.4.17
- **Icons**: RemixIcon (ri-* classes)
- **Build Tools**: PostCSS, Autoprefixer
- **Linting**: ESLint with Next.js config

## Project Structure
```
/
├── app/                 # Next.js App Router pages
│   ├── flights/        # Flight results page
│   ├── profile/        # User profile
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Home page
├── components/         # React components
│   ├── FlightSearch.tsx    # Main search component
│   ├── AirportSelector.tsx # Airport selection
│   └── [other components]
├── public/             # Static assets
└── [config files]
```

## Key Dependencies
- React 19 with latest features
- Next.js 15 with App Router
- TypeScript for type safety
- Tailwind CSS for styling
- No external calendar libraries (custom implementation)

## Development Environment
- Node.js project with npm
- TypeScript configuration
- ESLint for code quality
- Hot reload development server

## Styling Approach
- Tailwind CSS utility classes
- Responsive design with mobile-first approach
- Custom color scheme with primary blue (#013688)
- RemixIcon for consistent iconography
