
'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import FlightSearch from '@/components/FlightSearch';
import QuickServices from '@/components/QuickServices';
import OffersSection from '@/components/OffersSection';
import AirlinePartners from '@/components/AirlinePartners';
import Footer from '@/components/Footer';


export default function Page() {
  const [showMobileWarning, setShowMobileWarning] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      if (window.innerWidth < 1024) { // Show for tablet and mobile
        setShowMobileWarning(true);
      }
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return (
    <div className="min-h-screen">
      {/* Mobile Warning Popup */}
      {showMobileWarning && (
        <div className="fixed inset-0 bg-black/60 z-[200] flex items-center justify-center p-4">
          <div className="bg-white rounded-2xl shadow-2xl max-w-sm w-full p-6 text-center animate-fade-in">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="ri-computer-line text-2xl text-[#013688]"></i>
            </div>
            
            <h2 className="text-xl font-bold text-gray-900 mb-3">
              Desktop Optimized
            </h2>
            
            <p className="text-gray-600 mb-6 leading-relaxed">
              This site is only desktop optimized. Please check on desktop for the best experience.
            </p>
            
            <div className="space-y-3">
              <button
                onClick={() => setShowMobileWarning(false)}
                className="w-full bg-[#013688] text-white py-3 px-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
              >
                Continue Anyway
              </button>
              
              <button
                onClick={() => setShowMobileWarning(false)}
                className="w-full text-gray-500 py-2 text-sm hover:text-gray-700 transition-colors"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Background Image */}
      <div 
        className="relative bg-cover bg-center"
        style={{ 
          backgroundImage: `url(/hero.jpg)` 
        }}
      >
        
        {/* Header */}
        <div className="relative z-99">
          <Header />
        </div>

        {/* Flight Search Form */}
        <div className="relative z-19">
          <FlightSearch />
        </div>
      </div>

      {/* Quick Services */}
      <QuickServices />

      {/* Offers Section */}
      <OffersSection />

      {/* Airline Partners */}
      <AirlinePartners />

      {/* Footer */}
      <Footer />
    </div>
  );
}
