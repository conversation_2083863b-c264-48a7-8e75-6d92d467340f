# System Patterns

## Component Architecture
- **Functional Components**: Using React hooks for state management
- **Props Interface**: TypeScript interfaces for component props
- **State Management**: useState hooks for local component state
- **Event Handling**: Consistent event handler naming (handle*)

## FlightSearch Component Patterns
- **State Structure**: Multiple useState hooks for different UI states
- **Conditional Rendering**: Extensive use of ternary operators for UI states
- **Responsive Design**: Separate mobile/desktop layouts using Tailwind breakpoints
- **Modal/Popup Pattern**: Fixed positioning with backdrop for overlays

## Calendar Implementation Patterns
- **Date Generation**: Programmatic calendar day generation
- **Price Integration**: Each date object includes pricing information
- **Selection Logic**: Separate handling for one-way vs round-trip selections
- **Visual States**: Different styling for selected, in-range, and normal dates

## Styling Patterns
- **Utility Classes**: Heavy use of Tailwind utility classes
- **Responsive Modifiers**: md: prefix for desktop-specific styles
- **State-based Styling**: Dynamic classes based on component state
- **Color Scheme**: Consistent use of primary blue (#013688) and semantic colors

## Event Handling Patterns
- **Toggle Functions**: Boolean state toggles for UI elements
- **Selection Handlers**: Separate handlers for different selection types
- **Validation Logic**: Built-in validation for user inputs
- **Navigation**: Programmatic routing with Next.js router

## Data Flow Patterns
- **Props Down**: Parent-to-child data flow via props
- **Events Up**: Child-to-parent communication via callback props
- **Local State**: Component-level state for UI interactions
- **URL State**: Search parameters for shareable state
