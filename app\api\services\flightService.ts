/**
 * Main Flight Service
 * Combines list page and preview page services for backward compatibility
 * This file re-exports all functionality from the specialized service files
 */

// Re-export everything from list-page-services
export * from './list-page-services';
export * from './preview-page-service';

// Import the main services
import { listPageService } from './list-page-services';
import { previewPageService } from './preview-page-service';

// ============================================================================
// COMBINED FLIGHT SERVICE (Backward Compatibility)
// ============================================================================

export const flightService = {
  // List page functionality
  ...listPageService,
  
  // Preview page functionality
  ...previewPageService,
  
  // Alias methods for backward compatibility
  searchFlightsWithFormData: listPageService.searchFlights,
  searchFlightsWithFormDataAndSearchList: listPageService.searchFlightsWithFormDataAndSearchList,
  getSearchListData: listPageService.getSearchListData,
  getPreviewData: previewPageService.getPreviewData,
  
  // Additional utility methods
  async getFlightDetails(flightId: string): Promise<any> {
    try {
      console.log('🚀 Getting flight details for:', flightId);
      
      // This would typically call an API endpoint
      // For now, return mock data
      return {
        success: true,
        message: 'Flight details retrieved successfully',
        data: {
          id: flightId,
          airline: 'AI',
          flightNumber: 'AI 2412',
          departure: {
            time: '06:00',
            airport: 'DEL',
            city: 'Delhi',
            date: '2025-08-21'
          },
          arrival: {
            time: '08:30',
            airport: 'BOM',
            city: 'Mumbai',
            date: '2025-08-21'
          },
          duration: '2h 30m',
          price: 5500,
          currency: 'INR'
        }
      };
    } catch (error: any) {
      console.error('❌ Error getting flight details:', error);
      return {
        success: false,
        message: error.message || 'Failed to get flight details'
      };
    }
  },

  async bookFlight(bookingData: any): Promise<any> {
    try {
      console.log('🚀 Booking flight:', bookingData);
      
      // This would typically call a booking API endpoint
      // For now, return mock booking confirmation
      return {
        success: true,
        message: 'Flight booked successfully',
        data: {
          bookingReference: 'ECO' + Date.now(),
          pnr: 'PNR' + Math.random().toString(36).substr(2, 6).toUpperCase(),
          status: 'CONFIRMED',
          bookingDate: new Date().toISOString(),
          totalAmount: bookingData.totalAmount || 6000,
          currency: 'INR'
        }
      };
    } catch (error: any) {
      console.error('❌ Error booking flight:', error);
      return {
        success: false,
        message: error.message || 'Failed to book flight'
      };
    }
  }
};

// ============================================================================
// ADDITIONAL UTILITY FUNCTIONS
// ============================================================================

// Flight data normalization
export const normalizeFlights = (flights: any[]): any[] => {
  return flights.map(flight => ({
    ...flight,
    price: typeof flight.price === 'string' ? parseFloat(flight.price) : flight.price,
    duration: flight.duration || '0h 0m',
    stops: flight.stops || '0',
    rating: flight.rating || 4.0
  }));
};

// Flight deduplication
export const dedupeFlights = (flights: any[]): any[] => {
  const seen = new Set();
  return flights.filter(flight => {
    const key = `${flight.airline}-${flight.flightNumber}-${flight.departure.time}-${flight.price}`;
    if (seen.has(key)) {
      return false;
    }
    seen.add(key);
    return true;
  });
};

// Sort flights by price
export const sortFlightsByPrice = (flights: any[], ascending: boolean = true): any[] => {
  return [...flights].sort((a, b) => {
    const priceA = typeof a.price === 'string' ? parseFloat(a.price) : a.price;
    const priceB = typeof b.price === 'string' ? parseFloat(b.price) : b.price;
    return ascending ? priceA - priceB : priceB - priceA;
  });
};

// Flight details response interface
export interface FlightDetailsResponse {
  success: boolean;
  message: string;
  data?: any;
}

// Default export for convenience
export default flightService;
