
'use client';

import { useState } from 'react';
import BookingCard from './BookingCard';

export default function BookingTabs() {
  const [activeTab, setActiveTab] = useState('all');

  const tabs = [
    { id: 'all', label: 'All Bookings', count: 24 },
    { id: 'upcoming', label: 'Upcoming', count: 3 },
    { id: 'past', label: 'Past', count: 19 },
    { id: 'cancelled', label: 'Cancelled', count: 2 }
  ];

  const mockBookings = {
    all: [
      {
        id: 'FL001',
        pnr: 'ABC123',
        type: 'flight',
        status: 'upcoming',
        airline: 'Air India',
        route: 'New York → London',
        date: '2024-02-15',
        time: '10:30 AM',
        passenger: '<PERSON>',
        amount: '$850',
        bookingDate: '2024-01-20'
      },
      {
        id: 'FL002', 
        pnr: 'DEF456',
        type: 'flight',
        status: 'past',
        airline: 'Emirates',
        route: 'London → Dubai',
        date: '2024-01-10',
        time: '08:45 PM',
        passenger: '<PERSON>',
        amount: '$1,200',
        bookingDate: '2024-01-05'
      },
      {
        id: 'FL003',
        pnr: 'GHI789',
        type: 'flight', 
        status: 'upcoming',
        airline: 'Lufthansa',
        route: 'Paris → Berlin',
        date: '2024-03-20',
        time: '02:15 PM',
        passenger: 'John Smith',
        amount: '$450',
        bookingDate: '2024-01-25'
      },
      {
        id: 'FL004',
        pnr: 'JKL012',
        type: 'flight',
        status: 'cancelled',
        airline: 'British Airways',
        route: 'London → Madrid',
        date: '2024-02-05',
        time: '11:20 AM',
        passenger: 'John Smith',
        amount: '$380',
        bookingDate: '2024-01-15'
      }
    ]
  };

  mockBookings.upcoming = mockBookings.all.filter(booking => booking.status === 'upcoming');
  mockBookings.past = mockBookings.all.filter(booking => booking.status === 'past');
  mockBookings.cancelled = mockBookings.all.filter(booking => booking.status === 'cancelled');

  const currentBookings = mockBookings[activeTab] || [];

  return (
    <div className="bg-white rounded-xl shadow-lg p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-gray-900">My Bookings</h2>
        <button className="flex items-center space-x-2 px-4 py-2 text-[#013688] border border-[#013688] rounded-lg hover:bg-[#013688] hover:text-white transition-colors cursor-pointer">
          <div className="w-4 h-4 flex items-center justify-center">
            <i className="ri-download-line"></i>
          </div>
          <span className="whitespace-nowrap">Download All</span>
        </button>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-gray-100 p-1 rounded-lg">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 px-4 py-3 rounded-md text-sm font-semibold transition-all cursor-pointer whitespace-nowrap ${
              activeTab === tab.id
                ? 'bg-[#013688] text-white shadow-sm'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center space-x-2">
              <span>{tab.label}</span>
              <span className={`px-2 py-1 rounded-full text-xs ${
                activeTab === tab.id
                  ? 'bg-white/20 text-white'
                  : 'bg-gray-200 text-gray-600'
              }`}>
                {tab.count}
              </span>
            </div>
          </button>
        ))}
      </div>

      {/* Booking List */}
      <div className="space-y-4">
        {currentBookings.length > 0 ? (
          currentBookings.map((booking) => (
            <BookingCard key={booking.id} booking={booking} />
          ))
        ) : (
          <div className="text-center py-12">
            <div className="w-24 h-24 flex items-center justify-center bg-gray-100 rounded-full mx-auto mb-4">
              <i className="ri-inbox-line text-gray-400 text-3xl"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No bookings found</h3>
            <p className="text-gray-600 mb-6">You don't have any bookings in this category yet.</p>
            <button className="px-6 py-3 bg-[#013688] text-white rounded-lg hover:bg-[#012458] transition-colors cursor-pointer whitespace-nowrap">
              Book Your First Flight
            </button>
          </div>
        )}
      </div>

      {/* Pagination */}
      {currentBookings.length > 0 && (
        <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-200">
          <p className="text-sm text-gray-600">
            Showing {currentBookings.length} of {tabs.find(tab => tab.id === activeTab)?.count} bookings
          </p>
          <div className="flex space-x-2">
            <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 cursor-pointer">
              Previous
            </button>
            <button className="px-4 py-2 bg-[#013688] text-white rounded-lg hover:bg-[#012458] cursor-pointer">
              1
            </button>
            <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 cursor-pointer">
              2
            </button>
            <button className="px-4 py-2 border border-gray-300 rounded-lg text-gray-600 hover:bg-gray-50 cursor-pointer">
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
