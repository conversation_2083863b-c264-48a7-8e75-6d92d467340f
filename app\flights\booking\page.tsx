
'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { getAirlineLogo, getAirlineName, getDisplayFlightNumber } from '../../utils/airlineUtils';
import { previewPageService, PreviewData } from '@/app/api/services/preview-page-service';

function BookingPreview() {
  const searchParams = useSearchParams();
  const [selectedFlight, setSelectedFlight] = useState<any>(null);
  const [bookingData, setBookingData] = useState<any>(null);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [loading, setLoading] = useState(true);
  const [travelers, setTravelers] = useState([
    { id: 1, title: 'Mr.', firstName: '', lastName: '', gender: 'male', isAdult: true }
  ]);
  const [contactDetails, setContactDetails] = useState({
    email: '',
    phone: '',
    countryCode: '+91'
  });
  const [selectedAddons, setSelectedAddons] = useState({
    baggage: {},
    seats: {},
    meals: {},
    insurance: false,
    priorityCheckin: false
  });
  const [promoCode, setPromoCode] = useState('');
  const [appliedPromos, setAppliedPromos] = useState([]);
  const [showPromoList, setShowPromoList] = useState(false);
  const [activeSection, setActiveSection] = useState('flight-details');

  // Promo codes data
  const promoCodes = [
    { code: 'MMTSECURE', discount: 333, description: 'Get an instant discount of ₹333 on your flight booking and Trip Secure combo' },
    { code: 'AXISEMIDEAL', discount: 385, description: 'Get ₹385 Instant Discount and No-Cost EMI on paying with Axis Credit Card' },
    { code: 'HDFCDEALS', discount: 385, description: 'Use this code and get ₹385 on HDFC Credit cards' },
    { code: 'MMTHSBC', discount: 821, description: 'Get FLAT 12% discount on your flight booking with HSBC Credit Cards' },
    { code: 'MMTIDFCNEMI', discount: 684, description: 'Exclusive Offer on IDFC First Bank Credit Cards. Get ₹684 Off' },
    { code: 'HSBCDEALS', discount: 285, description: 'Use this code and get ₹285 on HSBC Credit cards' },
    { code: 'MMTSUPER', discount: 332, description: 'Get instant savings on your booking' }
  ];

  const addonServices = {
    baggage: [
      { weight: '5kg', price: 1200, selected: false },
      { weight: '10kg', price: 2200, selected: false },
      { weight: '15kg', price: 3000, selected: false }
    ],
    meals: [
      { name: 'Vegetarian', price: 350, selected: false },
      { name: 'Non-Vegetarian', price: 450, selected: false },
      { name: 'Jain Meal', price: 350, selected: false }
    ],
    seats: [
      { type: 'Standard', price: 0, selected: true },
      { type: 'Extra Legroom', price: 800, selected: false },
      { type: 'Window Seat', price: 300, selected: false },
      { type: 'Aisle Seat', price: 300, selected: false }
    ]
  };

  // Load flight data from localStorage and preview data from JSON
  useEffect(() => {
    const loadBookingData = async () => {
      try {
        setLoading(true);

        // Load selected flight from localStorage
        const savedFlightData = localStorage.getItem('selectedFlightForBooking');
        let flightBookingData = null;

        if (savedFlightData) {
          flightBookingData = JSON.parse(savedFlightData);
          setSelectedFlight(flightBookingData.flight);
          setBookingData(flightBookingData.bookingDetails);
        } else {
          // Fallback flight data if localStorage is empty
          setSelectedFlight({
            id: 'fallback-1',
            airline: '6E',
            logo: '/AirlineLogo/6E.png',
            flightNumber: '6E-234',
            departure: { time: '06:30', airport: 'DEL', city: 'Delhi', date: 'Friday, Aug 1' },
            arrival: { time: '09:15', airport: 'BLR', city: 'Bengaluru', date: 'Friday, Aug 1' },
            duration: '2h 45m',
            stops: 'Non-stop',
            price: 4250,
            originalPrice: 5500,
            baggage: { cabin: '7 kg', checkin: '15 kg' },
            cancellation: 'Free cancellation till 2 hours before departure',
            features: ['On-time', 'Meals', 'Wi-Fi'],
            fareType: 'SAVER'
          });
        }

        // Load preview data for fare breakdown, add-ons, etc.
        // Use flight details to get relevant preview data
        const fromCode = flightBookingData?.flight?.departure?.airport || 'BOM';
        const toCode = flightBookingData?.flight?.arrival?.airport || 'DEL';
        const tui = flightBookingData?.bookingDetails?.tui || undefined;

        const previewResponse = await previewPageService.getPreviewData(tui, fromCode, toCode);
        if (previewResponse.success && previewResponse.data) {
          setPreviewData(previewResponse.data);
        }

      } catch (error) {
        console.error('❌ Error loading booking data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadBookingData();
  }, []);

  const handleTravelerChange = (index, field, value) => {
    const updatedTravelers = [...travelers];
    updatedTravelers[index][field] = value;
    setTravelers(updatedTravelers);
  };

  const addTraveler = () => {
    const newTraveler = {
      id: travelers.length + 1,
      title: 'Mr.',
      firstName: '',
      lastName: '',
      gender: 'male',
      isAdult: true
    };
    setTravelers([...travelers, newTraveler]);
  };

  const handleContactChange = (field, value) => {
    setContactDetails(prev => ({ ...prev, [field]: value }));
  };

  const applyPromoCode = (code) => {
    const promo = promoCodes.find(p => p.code === code);
    if (promo && !appliedPromos.find(p => p.code === code)) {
      setAppliedPromos([...appliedPromos, promo]);
      setPromoCode('');
      setShowPromoList(false);
    }
  };

  const removePromo = (code) => {
    setAppliedPromos(appliedPromos.filter(p => p.code !== code));
  };

  const getTotalDiscount = () => {
    return appliedPromos.reduce((total, promo) => total + promo.discount, 0);
  };

  const getFinalPrice = () => {
    const basePrice = selectedFlight?.price || 0;
    const addonPrice = 0; // Calculate based on selected addons
    const discount = getTotalDiscount();

    // Use preview data for additional charges if available
    let additionalCharges = 0;
    if (previewData) {
      // Use NetAmount from preview data as additional reference
      // You can customize this logic based on your requirements
      const previewNetAmount = previewData.NetAmount || 0;
      const previewInsurance = 0; // Insurance premium from preview data
      additionalCharges = previewInsurance; // Add insurance premium as additional charge
    }

    return Math.max(0, basePrice + addonPrice + additionalCharges - discount);
  };

  const handleContinuePayment = () => {
    // Save booking details to localStorage for success page
    const bookingDetails = {
      flight: selectedFlight,
      travelers: travelers,
      contactDetails: contactDetails,
      addons: selectedAddons,
      promos: appliedPromos,
      finalPrice: getFinalPrice(),
      bookingDate: new Date().toISOString()
    };
    localStorage.setItem('completedBooking', JSON.stringify(bookingDetails));

    // Handle payment continuation
    window.location.href = '/flights/booking/success';
  };

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-[#013688] border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading booking details...</p>
        </div>
      </div>
    );
  }

  if (!selectedFlight) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <i className="ri-error-warning-line text-red-500 text-4xl mb-4"></i>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Flight Selected</h3>
          <p className="text-gray-600 mb-4">Please select a flight from the search results.</p>
          <a href="/flights" className="bg-[#013688] text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Back to Flight Search
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />

      <div className="pt-20 pb-16 px-4">
        <div className="max-w-7xl mx-auto">


          {/* Progress Header */}
          <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold text-gray-900">Complete your booking</h1>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Side - Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Flight Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                <div className="p-6 border-b border-gray-100">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">{selectedFlight.departure.city} → {selectedFlight.arrival.city}</h3>
                    <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-bold">CANCELLATION FEE WAIVER</span>
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <div className="text-sm text-gray-600">{selectedFlight.departure.date} • {selectedFlight.stops} • {selectedFlight.duration}</div>
                    <button className="text-[#013688] text-sm font-medium hover:underline">View Fare Rules</button>
                  </div>

                  {/* Flight Info */}
                  <div className="flex items-center space-x-4 mb-6">
                    <img src={getAirlineLogo(selectedFlight.airline, selectedFlight.logo)} alt={getAirlineName(selectedFlight.airline)} className="w-12 h-8 object-contain" />
                    <div>
                      <div className="font-semibold text-gray-900">{getAirlineName(selectedFlight.airline)}</div>
                      <div className="text-sm text-gray-500">{getDisplayFlightNumber(selectedFlight.airline, selectedFlight.flightNumber)} • Economy • {selectedFlight.fareType}</div>
                    </div>
                  </div>

                  {/* Flight Route */}
                  <div className="flex items-center justify-between mb-6">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900">{selectedFlight.departure.time}</div>
                      <div className="text-sm text-gray-600">{selectedFlight.departure.city}</div>
                      <div className="text-xs text-gray-500">{selectedFlight.departure.airport}</div>
                    </div>

                    <div className="flex-1 px-4">
                      <div className="relative">
                        <div className="h-px bg-gray-300 w-full"></div>
                        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white px-2">
                          <i className="ri-plane-line text-[#013688]"></i>
                        </div>
                      </div>
                      <div className="text-center mt-1">
                        <div className="text-sm text-gray-600">{selectedFlight.duration}</div>
                        <div className="text-xs text-green-600">{selectedFlight.stops}</div>
                      </div>
                    </div>

                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900">{selectedFlight.arrival.time}</div>
                      <div className="text-sm text-gray-600">{selectedFlight.arrival.city}</div>
                      <div className="text-xs text-gray-500">{selectedFlight.arrival.airport}</div>
                    </div>
                  </div>

                  {/* Baggage Info */}
                  <div className="flex items-center space-x-6 text-sm">
                    <div className="flex items-center space-x-2">
                      <i className="ri-suitcase-line text-gray-600"></i>
                      <span>Cabin Baggage: {selectedFlight.baggage.cabin}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <i className="ri-luggage-cart-line text-gray-600"></i>
                      <span>Check-In Baggage: {selectedFlight.baggage.checkin}</span>
                    </div>
                  </div>
                </div>

                {/* Additional Services */}
                <div className="p-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2 text-sm text-blue-600">
                      <i className="ri-shield-check-line"></i>
                      <span>Got excess baggage? Don't stress, buy extra check-in baggage allowance for BOM-CCJ at fab rates!</span>
                    </div>
                    <button className="text-[#013688] text-sm font-medium px-4 py-2 border border-[#013688] rounded-lg hover:bg-blue-50">ADD BAGGAGE</button>
                  </div>
                </div>
              </div>

              {/* Cancellation Policy */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Cancellation & Date Change Policy</h3>

                <div className="flex items-center space-x-4 mb-4">
                  <img src={getAirlineLogo(selectedFlight.airline, selectedFlight.logo)} alt={getAirlineName(selectedFlight.airline)} className="w-12 h-8 object-contain" />
                  <div>
                    <div className="font-semibold text-gray-900">{selectedFlight.departure.airport}-{selectedFlight.arrival.airport}</div>
                  </div>
                  <button className="text-[#013688] text-sm font-medium hover:underline ml-auto">View Policy</button>
                </div>

                {/* Cancellation Timeline */}
                <div className="relative">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Cancellation Penalty:</span>
                  </div>

                  <div className="relative h-2 bg-gradient-to-r from-green-400 via-yellow-400 via-orange-400 to-red-500 rounded-full mb-4"></div>

                  <div className="flex justify-between text-xs text-gray-600">
                    <div className="text-center">
                      <div className="font-semibold">₹0</div>
                      <div>Now</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">₹5,299</div>
                      <div>31 Jul 15:00</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">₹6,516</div>
                      <div>1 Aug 12:00</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold">₹6,516</div>
                      <div>1 Aug 15:00</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Travel Insurance */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Unsure of your travel plans? Get full flexibility with our special add-ons</h3>
                </div>

                <div className="border border-green-200 rounded-lg p-4 bg-green-50">
                  <div className="flex items-start space-x-3">
                    <input type="checkbox" className="mt-1 w-4 h-4 accent-[#013688]" />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="font-semibold text-gray-900">FlexiFly</span>
                        <span className="bg-green-600 text-white px-2 py-1 rounded text-xs font-bold">MMT SPECIAL</span>
                        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-bold">FLEXIFY INCLUDED</span>
                      </div>
                      <div className="text-sm text-gray-700 mb-2">
                        (Zero Cancellation or Free Date Change)
                      </div>
                      <div className="text-xs text-gray-600 mb-3">
                        Great! Get refund of up to ₹6,516 in case of cancellation up to 24 hours before departure! 
                        OR Save up to ₹3,299 on date change charges up to 3 hours before departure. You just pay the fare difference!
                      </div>
                      <div className="text-right">
                        <span className="text-lg font-bold text-gray-900">₹448</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Traveler Details Form */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Traveller Details</h3>

                {travelers.map((traveler, index) => (
                  <div key={traveler.id} className="border border-gray-200 rounded-lg p-4 mb-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-semibold text-gray-900">Traveller {index + 1} (Adult)</h4>
                      {index > 0 && (
                        <button 
                          onClick={() => setTravelers(travelers.filter((_, i) => i !== index))}
                          className="text-red-600 text-sm hover:underline"
                        >
                          Remove
                        </button>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                        <select 
                          value={traveler.title}
                          onChange={(e) => handleTravelerChange(index, 'title', e.target.value)}
                          className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#013688]"
                        >
                          <option value="Mr.">Mr.</option>
                          <option value="Ms.">Ms.</option>
                          <option value="Mrs.">Mrs.</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                        <input 
                          type="text"
                          value={traveler.firstName}
                          onChange={(e) => handleTravelerChange(index, 'firstName', e.target.value)}
                          placeholder="Enter first name"
                          className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#013688]"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                        <input 
                          type="text"
                          value={traveler.lastName}
                          onChange={(e) => handleTravelerChange(index, 'lastName', e.target.value)}
                          placeholder="Enter last name"
                          className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#013688]"
                        />
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                      <div className="flex items-center space-x-6">
                        <label className="flex items-center space-x-2">
                          <input 
                            type="radio" 
                            name={`gender-${index}`}
                            value="male"
                            checked={traveler.gender === 'male'}
                            onChange={(e) => handleTravelerChange(index, 'gender', e.target.value)}
                            className="w-4 h-4 accent-[#013688]"
                          />
                          <span className="text-sm">Male</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input 
                            type="radio" 
                            name={`gender-${index}`}
                            value="female"
                            checked={traveler.gender === 'female'}
                            onChange={(e) => handleTravelerChange(index, 'gender', e.target.value)}
                            className="w-4 h-4 accent-[#013688]"
                          />
                          <span className="text-sm">Female</span>
                        </label>
                      </div>
                    </div>
                  </div>
                ))}

                <button 
                  onClick={addTraveler}
                  className="text-[#013688] font-medium text-sm hover:underline"
                >
                  + Add Another Traveller
                </button>
              </div>

              {/* Contact Details */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Contact Details</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                    <input 
                      type="email"
                      value={contactDetails.email}
                      onChange={(e) => handleContactChange('email', e.target.value)}
                      placeholder="Enter email address"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#013688]"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Mobile Number</label>
                    <div className="flex">
                      <select 
                        value={contactDetails.countryCode}
                        onChange={(e) => handleContactChange('countryCode', e.target.value)}
                        className="p-3 border border-gray-300 rounded-l-lg focus:outline-none focus:border-[#013688] bg-gray-50 w-20"
                      >
                        <option value="+91">+91</option>
                        <option value="+1">+1</option>
                        <option value="+44">+44</option>
                      </select>
                      <input 
                        type="tel"
                        value={contactDetails.phone}
                        onChange={(e) => handleContactChange('phone', e.target.value)}
                        placeholder="Enter mobile number"
                        className="flex-1 p-3 border-t border-r border-b border-gray-300 rounded-r-lg focus:outline-none focus:border-[#013688]"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Addon Services */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-6">Add-on Services</h3>

                {/* Extra Baggage */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Extra Baggage</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {addonServices.baggage.map((item, index) => (
                      <label key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="checkbox" className="w-4 h-4 accent-[#013688]" />
                        <div className="flex-1">
                          <div className="font-medium">{item.weight}</div>
                          <div className="text-sm text-gray-600">₹{item.price}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Meals */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Meals</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {addonServices.meals.map((item, index) => (
                      <label key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="checkbox" className="w-4 h-4 accent-[#013688]" />
                        <div className="flex-1">
                          <div className="font-medium">{item.name}</div>
                          <div className="text-sm text-gray-600">₹{item.price}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Seat Selection */}
                <div className="mb-6">
                  <h4 className="font-semibold text-gray-900 mb-3">Seat Selection</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {addonServices.seats.map((item, index) => (
                      <label key={index} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input 
                          type="radio" 
                          name="seat-selection"
                          className="w-4 h-4 accent-[#013688]"
                          defaultChecked={item.selected}
                        />
                        <div className="flex-1">
                          <div className="font-medium">{item.type}</div>
                          <div className="text-sm text-gray-600">{item.price === 0 ? 'Free' : `₹${item.price}`}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Priority Check-in */}
                <div>
                  <label className="flex items-center space-x-3 p-4 border border-gray-200 rounded-lg cursor-pointer hover:bg-gray-50">
                    <input type="checkbox" className="w-4 h-4 accent-[#013688]" />
                    <div className="flex-1">
                      <div className="font-medium">Priority Check-in</div>
                      <div className="text-sm text-gray-600">Skip the queue with priority check-in - ₹599</div>
                    </div>
                  </label>
                </div>
              </div>

              {/* Important Information */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Important Information</h3>

                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <i className="ri-error-warning-fill text-red-500 mt-1"></i>
                    <div>
                      <div className="font-medium text-gray-900">Check travel guidelines and baggage information below:</div>
                      <div className="text-sm text-gray-600 mt-1">
                        • Carry no more than 1 check-in baggage and 1 hand baggage per passenger. If violated, airline may levy extra charges.
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <i className="ri-error-warning-fill text-red-500 mt-1"></i>
                    <div>
                      <div className="font-medium text-gray-900">Unaccompanied Minors Travelling:</div>
                      <div className="text-sm text-gray-600 mt-1">
                        Children below 18 years travelling alone may be classified as Unaccompanied Minors (UMNR) and can be subject to airline-specific UMNR rules, forms, and extra charges. Non-compliance may lead to denied boarding. Please check the respective airline website for exact requirements. Kindly note that MakeMyTrip will not have control over airline-specific decisions or charges.
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Continue Payment Button */}
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <button 
                  onClick={handleContinuePayment}
                  className="w-full bg-[#ff6b35] text-white py-4 rounded-lg text-lg font-bold hover:bg-red-600 transition-colors"
                >
                  CONTINUE TO PAYMENT
                </button>
              </div>
            </div>

            {/* Right Side - Fare Summary & Promo Codes */}
            <div className="lg:col-span-1">
              <div className="sticky top-24 space-y-6">
                {/* Fare Summary */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-6">Fare Summary</h3>

                  <div className="space-y-4 mb-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <i className="ri-user-line text-gray-600"></i>
                        <span className="text-sm text-gray-700">Base Fare</span>
                      </div>
                      <span className="font-semibold">₹{selectedFlight.price.toLocaleString()}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <i className="ri-money-rupee-circle-line text-gray-600"></i>
                        <span className="text-sm text-gray-700">Taxes and Surcharges</span>
                      </div>
                      <span className="font-semibold">₹848</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <i className="ri-service-line text-gray-600"></i>
                        <span className="text-sm text-gray-700">Other Services</span>
                      </div>
                      <span className="font-semibold">₹448</span>
                    </div>

                    {getTotalDiscount() > 0 && (
                      <div className="flex items-center justify-between text-green-600">
                        <div className="flex items-center space-x-2">
                          <i className="ri-price-tag-3-line"></i>
                          <span className="text-sm">Discounts</span>
                        </div>
                        <span className="font-semibold">-₹{getTotalDiscount()}</span>
                      </div>
                    )}

                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex items-center justify-between">
                        <span className="font-bold text-lg text-gray-900">Total Amount</span>
                        <span className="font-bold text-xl text-[#013688]">₹{getFinalPrice().toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Promo Codes */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-semibold text-gray-900">Coupons and Offers</h3>
                    <div className="w-8 h-8 flex items-center justify-center">
                      <i className="ri-gift-line text-2xl text-orange-500"></i>
                    </div>
                  </div>

                  {/* Promo Code Input */}
                  <div className="mb-6">
                    <div className="flex space-x-2 mb-4">
                      <input 
                        type="text"
                        value={promoCode}
                        onChange={(e) => setPromoCode(e.target.value)}
                        placeholder="Enter coupon code"
                        className="flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:border-[#013688] text-sm"
                      />
                      <button 
                        onClick={() => applyPromoCode(promoCode)}
                        className="bg-[#013688] text-white px-4 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors whitespace-nowrap text-sm"
                      >
                        Apply
                      </button>
                    </div>

                    {/* Applied Promo Codes */}
                    {appliedPromos.map((promo) => (
                      <div key={promo.code} className="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-3 mb-2">
                        <div>
                          <div className="font-semibold text-green-800 text-sm">{promo.code}</div>
                          <div className="text-xs text-green-600">₹{promo.discount} off applied</div>
                        </div>
                        <button 
                          onClick={() => removePromo(promo.code)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <i className="ri-close-line"></i>
                        </button>
                      </div>
                    ))}
                  </div>

                  {/* Promo Code Tabs */}
                  <div className="flex items-center space-x-1 mb-4 bg-gray-100 rounded-lg p-1">
                    <button className="flex-1 py-2 px-3 text-sm font-medium text-white bg-[#013688] rounded-md">All</button>
                    <button className="flex-1 py-2 px-3 text-sm font-medium text-gray-600 hover:text-gray-800">Bank</button>
                    <button className="flex-1 py-2 px-3 text-sm font-medium text-gray-600 hover:text-gray-800">Add-ons</button>
                  </div>

                  {/* Available Promo Codes */}
                  <div className="space-y-3 max-h-80 overflow-y-auto">
                    {promoCodes.map((promo) => {
                      const isApplied = appliedPromos.find(p => p.code === promo.code);
                      return (
                        <div key={promo.code} className={`border rounded-lg p-3 ${isApplied ? 'border-green-200 bg-green-50' : 'border-gray-200'}`}>
                          <div className="flex items-center justify-between mb-2">
                            <div className="flex items-center space-x-2">
                              <i className="ri-price-tag-3-line text-[#013688]"></i>
                              <span className="font-semibold text-sm">{promo.code}</span>
                            </div>
                            <span className="font-bold text-sm text-[#013688]">₹{promo.discount} off</span>
                          </div>
                          <div className="text-xs text-gray-600 mb-3">{promo.description}</div>
                          <button 
                            onClick={() => applyPromoCode(promo.code)}
                            disabled={isApplied}
                            className={`w-full py-2 px-4 rounded-lg text-sm font-semibold transition-all ${
                              isApplied 
                                ? 'bg-green-100 text-green-800 cursor-not-allowed' 
                                : 'bg-[#013688] text-white hover:bg-blue-700'
                            }`}
                          >
                            {isApplied ? 'Applied' : 'Apply'}
                          </button>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Trip Secure */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <i className="ri-shield-check-line text-2xl text-blue-600"></i>
                      <span className="font-semibold text-gray-900">Trip Secure</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <img src="https://readdy.ai/api/search-image?query=American%20Express%20credit%20card%20logo%20blue%20and%20white&width=40&height=24&seq=amex-logo&orientation=landscape" alt="Amex" className="w-10 h-6 object-contain" />
                    </div>
                  </div>

                  <div className="text-sm text-gray-700 mb-4">
                    <span className="font-semibold">₹549</span>/traveller (18% GST included)
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <div className="font-medium mb-1">24x7 Support</div>
                      <div className="text-gray-600">Upto ₹ 50,000</div>
                    </div>
                    <div>
                      <div className="font-medium mb-1">Trip Cancellation</div>
                      <div className="text-gray-600">Upto ₹ 3,500</div>
                    </div>
                  </div>

                  <button className="w-full mt-4 text-[#013688] text-sm font-medium hover:underline">
                    View All
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}

export default function BookingPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin w-8 h-8 border-2 border-[#013688] border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-gray-600">Loading booking details...</p>
        </div>
      </div>
    }>
      <BookingPreview />
    </Suspense>
  );
}
