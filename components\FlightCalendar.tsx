'use client';

import { useState, useEffect, useRef } from 'react';

export default function FlightCalendar({
  isOpen,
  onClose,
  onDateSelect,
  tripType = 'oneWay',
  selectedDepartureDate,
  selectedReturnDate,
  dateSelectionStep = 'departure'
}) {
  const [selectedMonthIndex, setSelectedMonthIndex] = useState(0);
  const calendarRef = useRef<HTMLDivElement>(null);

  // Get current date and generate months dynamically
  const today = new Date();
  const currentYear = today.getFullYear();
  const currentMonth = today.getMonth();

  const months = [
    {
      name: `${new Date(currentYear, currentMonth).toLocaleString('default', { month: 'long' })} ${currentYear}`,
      short: `${new Date(currentYear, currentMonth).toLocaleString('default', { month: 'short' })}'${currentYear.toString().slice(-2)}`,
      index: 0,
      actualMonth: currentMonth,
      actualYear: currentYear
    },
    {
      name: `${new Date(currentYear, currentMonth + 1).toLocaleString('default', { month: 'long' })} ${currentMonth + 1 > 11 ? currentYear + 1 : currentYear}`,
      short: `${new Date(currentYear, currentMonth + 1).toLocaleString('default', { month: 'short' })}'${(currentMonth + 1 > 11 ? currentYear + 1 : currentYear).toString().slice(-2)}`,
      index: 1,
      actualMonth: currentMonth + 1 > 11 ? 0 : currentMonth + 1,
      actualYear: currentMonth + 1 > 11 ? currentYear + 1 : currentYear
    },
    {
      name: `${new Date(currentYear, currentMonth + 2).toLocaleString('default', { month: 'long' })} ${currentMonth + 2 > 11 ? currentYear + 1 : currentYear}`,
      short: `${new Date(currentYear, currentMonth + 2).toLocaleString('default', { month: 'short' })}'${(currentMonth + 2 > 11 ? currentYear + 1 : currentYear).toString().slice(-2)}`,
      index: 2,
      actualMonth: currentMonth + 2 > 11 ? (currentMonth + 2) - 12 : currentMonth + 2,
      actualYear: currentMonth + 2 > 11 ? currentYear + 1 : currentYear
    }
  ];

  // Generate calendar days with pricing
  const generateCalendarDays = (monthIndex) => {
    const days = [];
    const monthData = months[monthIndex];
    const year = monthData.actualYear;
    const month = monthData.actualMonth;
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

    // Get the first day of the month (0 = Sunday, 1 = Monday, etc.)
    const firstDayOfMonth = new Date(year, month, 1).getDay();

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      days.push(null);
    }

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dayName = dayNames[date.getDay()];
      const basePrice = 2000 + Math.floor(Math.random() * 2000);
      const discount = Math.floor(Math.random() * 300);
      const price = basePrice - discount;

      // Check if date is in the past
      const todayStart = new Date(today);
      todayStart.setHours(0, 0, 0, 0);
      const isPastDate = date < todayStart;

      days.push({
        day: day.toString().padStart(2, '0'),
        month: monthData.short,
        monthName: monthData.name,
        dayName,
        price,
        isLowestPrice: Math.random() > 0.85,
        date: date,
        isPastDate: isPastDate,
        isToday: date.toDateString() === today.toDateString()
      });
    }
    return days;
  };

  // Click outside to close
  useEffect(() => {
    // Check if we're in the browser environment
    if (typeof window === 'undefined') return;

    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // Navigation functions
  const nextMonth = () => {
    setSelectedMonthIndex(prev => (prev < 2 ? prev + 1 : prev));
  };

  const prevMonth = () => {
    setSelectedMonthIndex(prev => (prev > 0 ? prev - 1 : prev));
  };

  // Check if date is selected (updated to match across months)
  const isSelectedDate = (dateInfo) => {
    if (!dateInfo) return false;

    const currentDate = parseInt(dateInfo.day);
    const depDate = selectedDepartureDate ? parseInt(selectedDepartureDate.day) : null;
    const retDate = selectedReturnDate ? parseInt(selectedReturnDate.day) : null;

    if (tripType === 'roundTrip') {
      return currentDate === depDate || currentDate === retDate;
    }
    return currentDate === depDate;
  };

  // New function to check if date should be highlighted across months
  const isDateHighlighted = (dateInfo) => {
    if (!dateInfo) return false;

    const currentDate = parseInt(dateInfo.day);
    const depDate = selectedDepartureDate ? parseInt(selectedDepartureDate.day) : null;
    const retDate = selectedReturnDate ? parseInt(selectedReturnDate.day) : null;

    // Highlight same date number across all months
    if (tripType === 'roundTrip') {
      return currentDate === depDate || currentDate === retDate;
    }
    return currentDate === depDate;
  };

  // New function to check if date is the exact selected date (same month)
  const isExactSelectedDate = (dateInfo) => {
    if (!dateInfo || !selectedDepartureDate && !selectedReturnDate) return false;

    const currentDate = parseInt(dateInfo.day);
    const currentMonth = dateInfo.month;

    const depDate = selectedDepartureDate ? parseInt(selectedDepartureDate.day) : null;
    const depMonth = selectedDepartureDate ? selectedDepartureDate.month : null;

    const retDate = selectedReturnDate ? parseInt(selectedReturnDate.day) : null;
    const retMonth = selectedReturnDate ? selectedReturnDate.month : null;

    if (tripType === 'roundTrip') {
      return (currentDate === depDate && currentMonth === depMonth) ||
             (currentDate === retDate && currentMonth === retMonth);
    }
    return currentDate === depDate && currentMonth === depMonth;
  };

  // Check if date is in range (for round trip)
  const isDateInRange = (dateInfo) => {
    if (!dateInfo || tripType !== 'roundTrip' || !selectedDepartureDate || !selectedReturnDate) return false;

    const depDate = parseInt(selectedDepartureDate.day);
    const currentDate = parseInt(dateInfo.day);
    const retDate = parseInt(selectedReturnDate.day);

    return currentDate >= Math.min(depDate, retDate) && currentDate <= Math.max(depDate, retDate);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-[60] flex items-end md:items-center justify-center p-0 md:p-4">
      <div
        ref={calendarRef}
        className="bg-white rounded-t-2xl md:rounded-lg shadow-xl w-full md:max-w-4xl max-h-[85vh] md:max-h-[80vh] overflow-hidden"
      >
        {/* Calendar Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-100">
          <div className="flex items-center space-x-4">
            <button
              onClick={prevMonth}
              disabled={selectedMonthIndex === 0}
              className={`p-2 rounded-full ${selectedMonthIndex === 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              <i className="ri-arrow-left-line text-lg"></i>
            </button>

            <div className="text-lg font-semibold text-gray-800">
              <span className="md:hidden">{months[selectedMonthIndex].name}</span>
              <span className="hidden md:block">
                {months[selectedMonthIndex].name} - {months[Math.min(selectedMonthIndex + 1, 2)].name}
              </span>
            </div>

            <button
              onClick={nextMonth}
              disabled={selectedMonthIndex === 2}
              className={`p-2 rounded-full ${selectedMonthIndex === 2 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-600 hover:bg-gray-100'}`}
            >
              <i className="ri-arrow-right-line text-lg"></i>
            </button>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-full transition-all"
          >
            <i className="ri-close-line text-xl text-gray-600"></i>
          </button>
        </div>

        {/* Date Selection Instructions for Round Trip */}
        {tripType === 'roundTrip' && (
          <div className="px-4 py-3 bg-blue-50 border-b border-blue-100">
            <div className="text-sm font-semibold text-blue-800">
              {dateSelectionStep === 'departure' 
                ? '1. Select departure date' 
                : '2. Select return date'
              }
            </div>
            <div className="text-xs text-blue-600 mt-1">
              {dateSelectionStep === 'departure' 
                ? 'Choose your outbound flight date'
                : 'Choose your return flight date'
              }
            </div>
          </div>
        )}

        {/* Calendar Content */}
        <div className="overflow-y-auto max-h-[calc(85vh-120px)] md:max-h-[calc(80vh-120px)]">
          {/* Mobile: Single Month with Scroll */}
          <div className="md:hidden">
            {[0, 1, 2].map((monthIdx) => (
              <div key={monthIdx} className="p-4 border-b border-gray-100 last:border-b-0">
                <div className="text-lg font-semibold text-gray-800 mb-4 text-center">
                  {months[monthIdx].name}
                </div>
                
                {/* Day headers */}
                <div className="grid grid-cols-7 gap-1 mb-3">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                    <div key={day} className="text-center text-xs font-semibold text-gray-500 py-2">
                      {day}
                    </div>
                  ))}
                </div>
 
                {/* Calendar dates */}
                <div className="grid grid-cols-7 gap-1">
                  {generateCalendarDays(monthIdx).map((date, index) => {
                    if (!date) {
                      return <div key={index} className="p-1.5"></div>; // Empty cell for padding
                    }

                    const exactSelected = isExactSelectedDate(date);
                    const highlighted = isDateHighlighted(date);
                    const inRange = isDateInRange(date);

                    return (
                      <button
                        key={index}
                        onClick={() => date.isPastDate ? null : onDateSelect(date)}
                        disabled={date.isPastDate}
                        className={`p-1.5 text-center rounded transition-all border relative ${
                          date.isPastDate
                            ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                            : date.isToday
                            ? 'bg-green-100 text-green-800 border-green-300 ring-2 ring-green-200'
                            : exactSelected
                            ? 'bg-[#013688] text-white border-[#013688]'
                            : highlighted && !exactSelected
                            ? 'bg-blue-200 text-blue-900 border-blue-300'
                            : inRange
                            ? 'bg-blue-100 text-blue-800 border-blue-200'
                            : 'hover:bg-blue-50 border-gray-200 hover:border-blue-200'
                        }`}
                      >
                        <div className={`text-sm font-semibold ${
                          date.isPastDate ? 'text-gray-400' :
                          date.isToday ? 'text-green-800' :
                          exactSelected ? 'text-white' :
                          highlighted ? 'text-blue-900' : 'text-gray-900'
                        }`}>
                          {date.day}
                        </div>
                        {!date.isPastDate && (
                          <div className={`text-xs font-semibold ${
                            date.isToday ? 'text-green-700' :
                            exactSelected ? 'text-white' :
                            highlighted ? 'text-blue-900' :
                            date.isLowestPrice ? 'text-green-600' : 'text-gray-600'
                          }`}>
                            ₹{date.price.toLocaleString()}
                          </div>
                        )}
                        {date.isToday && (
                          <div className="absolute -top-1 -left-1 text-xs font-bold text-green-600 bg-white rounded-full px-1">
                            TODAY
                          </div>
                        )}
                        {date.isLowestPrice && !exactSelected && !highlighted && !date.isPastDate && !date.isToday && (
                          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"></div>
                        )}
                        {highlighted && !exactSelected && !date.isPastDate && (
                          <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>

          {/* Desktop: Two Months Side by Side */}
          <div className="hidden md:block p-4">
            <div className="grid grid-cols-2 gap-6">
              {[selectedMonthIndex, Math.min(selectedMonthIndex + 1, 2)].map((monthIdx) => (
                <div key={monthIdx}>
                  <div className="text-lg font-semibold text-gray-800 mb-4 text-center">
                    {months[monthIdx].name}
                  </div>
                  
                  {/* Day headers */}
                  <div className="grid grid-cols-7 gap-1 mb-3">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                      <div key={day} className="text-center text-xs font-semibold text-gray-500 py-2">
                        {day}
                      </div>
                    ))}
                  </div>

                  {/* Calendar dates */}
                  <div className="grid grid-cols-7 gap-1">
                    {generateCalendarDays(monthIdx).map((date, index) => {
                      if (!date) {
                        return <div key={index} className="p-2"></div>; // Empty cell for padding
                      }

                      const exactSelected = isExactSelectedDate(date);
                      const highlighted = isDateHighlighted(date);
                      const inRange = isDateInRange(date);

                      return (
                        <button
                          key={index}
                          onClick={() => date.isPastDate ? null : onDateSelect(date)}
                          disabled={date.isPastDate}
                          className={`p-2 text-center rounded transition-all border relative ${
                            date.isPastDate
                              ? 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                              : date.isToday
                              ? 'bg-green-100 text-green-800 border-green-300 ring-2 ring-green-200'
                              : exactSelected
                              ? 'bg-[#013688] text-white border-[#013688]'
                              : highlighted && !exactSelected
                              ? 'bg-blue-200 text-blue-900 border-blue-300'
                              : inRange
                              ? 'bg-blue-100 text-blue-800 border-blue-200'
                              : 'hover:bg-blue-50 border-gray-200 hover:border-blue-200'
                          }`}
                        >
                          <div className={`text-sm font-semibold ${
                            date.isPastDate ? 'text-gray-400' :
                            date.isToday ? 'text-green-800' :
                            exactSelected ? 'text-white' :
                            highlighted ? 'text-blue-900' : 'text-gray-900'
                          }`}>
                            {date.day}
                          </div>
                          {!date.isPastDate && (
                            <div className={`text-xs font-semibold ${
                              date.isToday ? 'text-green-700' :
                              exactSelected ? 'text-white' :
                              highlighted ? 'text-blue-900' :
                              date.isLowestPrice ? 'text-green-600' : 'text-gray-600'
                            }`}>
                              ₹{date.price.toLocaleString()}
                            </div>
                          )}
                          {date.isToday && (
                            <div className="absolute -top-1 -left-1 text-xs font-bold text-green-600 bg-white rounded-full px-1">
                              TODAY
                            </div>
                          )}
                          {date.isLowestPrice && !exactSelected && !highlighted && !date.isPastDate && !date.isToday && (
                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full"></div>
                          )}
                          {highlighted && !exactSelected && !date.isPastDate && (
                            <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Price Legend */}
        <div className="border-t border-gray-100 p-4 bg-gray-50">
          <div className="flex items-center justify-center flex-wrap gap-x-6 gap-y-2 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-100 border-2 border-green-300 rounded-full"></div>
              <span className="text-gray-600">Today</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-gray-600">Lowest Price</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-[#013688] rounded-full"></div>
              <span className="text-gray-600">Selected</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-blue-200 border border-blue-300 rounded-full"></div>
              <span className="text-gray-600">Same Date</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-gray-100 border border-gray-200 rounded-full"></div>
              <span className="text-gray-600">Past Date</span>
            </div>
            {tripType === 'roundTrip' && (
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-100 border border-blue-200 rounded-full"></div>
                <span className="text-gray-600">Date Range</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
