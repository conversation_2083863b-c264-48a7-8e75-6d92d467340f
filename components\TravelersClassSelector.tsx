'use client';

import { useState, useEffect, useRef } from 'react';

export default function TravelersClassSelector({
  isOpen,
  onClose,
  travelers,
  onTravelersChange,
  selectedClass,
  onClassChange,
  isMobile = false
}) {
  const dropdownRef = useRef(null);

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('touchstart', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('touchstart', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handleTravelerChange = (type, operation) => {
    const newCount = operation === 'increment' 
      ? Math.min(travelers[type] + 1, type === 'adults' ? 9 : type === 'children' ? 8 : 2)
      : Math.max(travelers[type] - 1, type === 'adults' ? 1 : 0);
    
    onTravelersChange({ ...travelers, [type]: newCount });
  };

  const getTotalTravelers = () => {
    return travelers.adults + travelers.children + travelers.infants;
  };

  const getClassDisplay = () => {
    const classNames = {
      economy: 'Economy',
      premiumEconomy: 'Premium Economy',
      business: 'Business',
      first: 'First'
    };
    return classNames[selectedClass] || 'Economy';
  };

  if (!isOpen) return null;

  // Mobile Bottom Sheet
  if (isMobile) {
    return (
      <div className="fixed inset-0 bg-black/50 z-[70] flex items-end justify-center">
        <div
          ref={dropdownRef}
          className="bg-white rounded-t-2xl shadow-xl w-full max-h-[60vh] overflow-hidden animate-slide-up"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-2.5 border-b border-gray-100">
            <h3 className="text-base font-semibold text-gray-800">Travelers & Class</h3>
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-100 rounded-full transition-all"
            >
              <i className="ri-close-line text-lg text-gray-600"></i>
            </button>
          </div>

          {/* Content */}
          <div className="overflow-y-auto max-h-[40vh] p-2.5">
            {/* Travelers Section */}
            <div className="space-y-1 mb-2.5">
              <div className="text-sm font-semibold text-gray-800 mb-1">Travelers</div>

              {/* Adults */}
              <div className="flex items-center justify-between py-1 px-1.5 hover:bg-gray-50 rounded-lg transition-all">
                <div className="flex-1">
                  <div className="font-medium text-gray-800 text-sm">Adults</div>
                  <div className="text-xs text-gray-500">12+ years</div>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => handleTravelerChange('adults', 'decrement')}
                    disabled={travelers.adults <= 1}
                    className="w-6 h-6 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
                  >
                    <i className="ri-subtract-line text-xs"></i>
                  </button>
                  <span className="w-5 text-center font-semibold text-sm">{travelers.adults}</span>
                  <button
                    onClick={() => handleTravelerChange('adults', 'increment')}
                    disabled={travelers.adults >= 9}
                    className="w-6 h-6 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
                  >
                    <i className="ri-add-line text-xs"></i>
                  </button>
                </div>
              </div>

              {/* Children */}
              <div className="flex items-center justify-between py-1 px-1.5 hover:bg-gray-50 rounded-lg transition-all">
                <div className="flex-1">
                  <div className="font-medium text-gray-800 text-sm">Children</div>
                  <div className="text-xs text-gray-500">2-12 years</div>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => handleTravelerChange('children', 'decrement')}
                    disabled={travelers.children <= 0}
                    className="w-6 h-6 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
                  >
                    <i className="ri-subtract-line text-xs"></i>
                  </button>
                  <span className="w-5 text-center font-semibold text-sm">{travelers.children}</span>
                  <button
                    onClick={() => handleTravelerChange('children', 'increment')}
                    disabled={travelers.children >= 8}
                    className="w-6 h-6 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
                  >
                    <i className="ri-add-line text-xs"></i>
                  </button>
                </div>
              </div>

              {/* Infants */}
              <div className="flex items-center justify-between py-1 px-1.5 hover:bg-gray-50 rounded-lg transition-all">
                <div className="flex-1">
                  <div className="font-medium text-gray-800 text-sm">Infants</div>
                  <div className="text-xs text-gray-500">0-2 years</div>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => handleTravelerChange('infants', 'decrement')}
                    disabled={travelers.infants <= 0}
                    className="w-6 h-6 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
                  >
                    <i className="ri-subtract-line text-xs"></i>
                  </button>
                  <span className="w-5 text-center font-semibold text-sm">{travelers.infants}</span>
                  <button
                    onClick={() => handleTravelerChange('infants', 'increment')}
                    disabled={travelers.infants >= 2}
                    className="w-6 h-6 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
                  >
                    <i className="ri-add-line text-xs"></i>
                  </button>
                </div>
              </div>
            </div>

            {/* Class Section */}
            <div className="border-t border-gray-100 pt-2">
              <div className="text-sm font-semibold text-gray-800 mb-1">Travel Class</div>
              <div className="grid grid-cols-2 gap-1">
                {[
                  { id: 'economy', name: 'Economy', desc: 'Lowest fare' },
                  { id: 'premiumEconomy', name: 'Premium Economy', desc: 'Extra legroom' },
                  { id: 'business', name: 'Business', desc: 'Priority boarding' },
                  { id: 'first', name: 'First Class', desc: 'Luxury experience' }
                ].map((classOption) => (
                  <label key={classOption.id} className="flex items-start space-x-1 cursor-pointer p-1 hover:bg-gray-50 rounded transition-all border border-gray-200">
                    <input
                      type="radio"
                      name="travelClass"
                      value={classOption.id}
                      checked={selectedClass === classOption.id}
                      onChange={(e) => onClassChange(e.target.value)}
                      className="accent-[#013688] w-3 h-3 mt-0.5"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-800 text-xs leading-tight">{classOption.name}</div>
                      <div className="text-xs text-gray-500 leading-tight truncate">{classOption.desc}</div>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>

          {/* Done Button */}
          <div className="border-t border-gray-100 p-2">
            <button
              onClick={onClose}
              className="w-full bg-[#013688] text-white py-2 rounded-lg font-semibold hover:bg-blue-700 transition-all text-sm"
            >
              Done
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Desktop Dropdown
  return (
    <div
      ref={dropdownRef}
      className="absolute top-full right-0 w-[300px] bg-white border border-gray-200 rounded-lg shadow-xl z-[60] mt-2 max-h-[300px] overflow-hidden"
    >
      <div className="p-2 overflow-y-auto max-h-[280px]">
        {/* Travelers Section */}
        <div className="space-y-1 mb-2">
          <div className="text-sm font-semibold text-gray-800 mb-1">Travelers</div>

          {/* Adults */}
          <div className="flex items-center justify-between py-0.5 px-1 hover:bg-gray-50 rounded-lg transition-all">
            <div className="flex-1">
              <div className="font-medium text-gray-800 text-sm">Adults</div>
              <div className="text-xs text-gray-500">12+ years</div>
            </div>
            <div className="flex items-center space-x-1">
              <button
                onClick={() => handleTravelerChange('adults', 'decrement')}
                disabled={travelers.adults <= 1}
                className="w-5 h-5 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
              >
                <i className="ri-subtract-line text-xs"></i>
              </button>
              <span className="w-4 text-center font-semibold text-sm">{travelers.adults}</span>
              <button
                onClick={() => handleTravelerChange('adults', 'increment')}
                disabled={travelers.adults >= 9}
                className="w-5 h-5 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
              >
                <i className="ri-add-line text-xs"></i>
              </button>
            </div>
          </div>

          {/* Children */}
          <div className="flex items-center justify-between py-0.5 px-1 hover:bg-gray-50 rounded-lg transition-all">
            <div className="flex-1">
              <div className="font-medium text-gray-800 text-sm">Children</div>
              <div className="text-xs text-gray-500">2-12 years</div>
            </div>
            <div className="flex items-center space-x-1">
              <button
                onClick={() => handleTravelerChange('children', 'decrement')}
                disabled={travelers.children <= 0}
                className="w-5 h-5 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
              >
                <i className="ri-subtract-line text-xs"></i>
              </button>
              <span className="w-4 text-center font-semibold text-sm">{travelers.children}</span>
              <button
                onClick={() => handleTravelerChange('children', 'increment')}
                disabled={travelers.children >= 8}
                className="w-5 h-5 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
              >
                <i className="ri-add-line text-xs"></i>
              </button>
            </div>
          </div>

          {/* Infants */}
          <div className="flex items-center justify-between py-0.5 px-1 hover:bg-gray-50 rounded-lg transition-all">
            <div className="flex-1">
              <div className="font-medium text-gray-800 text-sm">Infants</div>
              <div className="text-xs text-gray-500">0-2 years</div>
            </div>
            <div className="flex items-center space-x-1">
              <button
                onClick={() => handleTravelerChange('infants', 'decrement')}
                disabled={travelers.infants <= 0}
                className="w-5 h-5 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
              >
                <i className="ri-subtract-line text-xs"></i>
              </button>
              <span className="w-4 text-center font-semibold text-sm">{travelers.infants}</span>
              <button
                onClick={() => handleTravelerChange('infants', 'increment')}
                disabled={travelers.infants >= 2}
                className="w-5 h-5 border border-gray-300 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed hover:border-[#013688] transition-all"
              >
                <i className="ri-add-line text-xs"></i>
              </button>
            </div>
          </div>
        </div>

        {/* Class Section */}
        <div className="border-t border-gray-100 pt-2">
          <div className="text-sm font-semibold text-gray-800 mb-1">Travel Class</div>
          <div className="grid grid-cols-2 gap-0.5">
            {[
              { id: 'economy', name: 'Economy', desc: 'Lowest fare' },
              { id: 'premiumEconomy', name: 'Premium Economy', desc: 'Extra legroom' },
              { id: 'business', name: 'Business', desc: 'Priority boarding' },
              { id: 'first', name: 'First Class', desc: 'Luxury experience' }
            ].map((classOption) => (
              <label key={classOption.id} className="flex items-start space-x-0.5 cursor-pointer p-0.5 hover:bg-gray-50 rounded transition-all border border-gray-200">
                <input
                  type="radio"
                  name="travelClass"
                  value={classOption.id}
                  checked={selectedClass === classOption.id}
                  onChange={(e) => onClassChange(e.target.value)}
                  className="accent-[#013688] w-3 h-3 mt-0.5"
                />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-gray-800 text-xs leading-tight">{classOption.name}</div>
                  <div className="text-xs text-gray-500 leading-tight truncate">{classOption.desc}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Done Button */}
        <div className="border-t border-gray-100 pt-2 mt-2">
          <button
            onClick={onClose}
            className="w-full bg-[#013688] text-white py-1.5 rounded-lg font-semibold hover:bg-blue-700 transition-all text-sm"
          >
            Done
          </button>
        </div>
      </div>
    </div>
  );
}