# Multi-Provider Flight Search Processing Flow

## Overview

This document describes the correct step-by-step flow for handling flight search results from multiple providers with proper boolean handling, deduplication, sorting, filtering, and pagination.

## Core Concepts

### Input Parameters

- **`flightList: Flight[]`** → New flights from a provider
- **`isMaster: boolean`** → 
  - `false` = Master list is empty (first provider)
  - `true` = Master list already has data (subsequent provider)

### Flow Branching

The system uses the `isMaster` boolean to determine which processing path to follow:

## Case A: First Provider (isMaster = false)

When `isMaster = false`, the master list is empty and this is the first provider response.

### Step-by-Step Process:

1. **Normalize Incoming Flights**
   ```typescript
   const normalizedFlights = normalizeFlights(incomingFlights);
   ```
   - Validates required fields
   - Applies fallbacks for missing data
   - Ensures consistent data structure

2. **Sort Flights by Lowest Price**
   ```typescript
   const sortedFlights = sortFlightsByPrice(normalizedFlights);
   ```
   - Primary sort: Price (ascending)
   - Secondary sort: Duration (ascending)

3. **Save Sorted Flights into Master List**
   ```typescript
   masterState.flights = sortedFlights;
   ```

4. **Build Filter Options**
   ```typescript
   const filterOptions = buildFilterOptions(sortedFlights);
   ```
   - Extract unique airlines
   - Calculate price range (min/max)
   - Calculate duration range
   - Extract available stops
   - Set time slot options

5. **Apply Active Filters**
   ```typescript
   const filteredFlights = applyFilters(sortedFlights, activeFilters);
   ```
   - Filter by selected airlines
   - Filter by price range
   - Filter by departure/arrival time slots
   - Filter by stops
   - Filter by refundable status

6. **Paginate Filtered Flights**
   ```typescript
   const paginatedResult = paginateFlights(filteredFlights, page, pageSize);
   ```
   - Default: 20 flights per page
   - Returns current page data
   - Includes pagination metadata

7. **Display First Batch**
   - Return paginated flights for immediate display
   - Include filter options for UI
   - Include pagination controls

## Case B: Subsequent Providers (isMaster = true)

When `isMaster = true`, the master list already contains flights from previous providers.

### Step-by-Step Process:

1. **Normalize Incoming Flights**
   ```typescript
   const normalizedFlights = normalizeFlights(incomingFlights);
   ```

2. **Merge with Existing Master List**
   ```typescript
   const combinedFlights = [...existingMasterFlights, ...normalizedFlights];
   ```

3. **Deduplicate Combined List**
   ```typescript
   const uniqueFlights = dedupeFlights(combinedFlights);
   ```
   - Uses composite key: `airline-flightNumber-departureTime-date-arrivalTime`
   - Also checks flight ID for additional safety
   - Logs removed duplicates

4. **Sort Combined Master List**
   ```typescript
   const sortedMasterList = sortFlightsByPrice(uniqueFlights);
   ```
   - Re-sort entire list by price
   - Maintains consistent ordering

5. **Update Filter Options**
   ```typescript
   const updatedFilterOptions = buildFilterOptions(sortedMasterList);
   ```
   - Include new airlines from additional provider
   - Update price range if new min/max found
   - Update available stops
   - Refresh all filter options

6. **✅ STOP HERE**
   - **Do NOT re-run filters or pagination in this step**
   - Return updated master state with `shouldApplyFilters: true`
   - Let the UI handle reapplying active filters

## Helper Functions

### `normalizeFlights(flights: Flight[]): Flight[]`
- Validates and normalizes flight data structure
- Applies fallbacks for missing required fields
- Handles edge cases and malformed data

### `dedupeFlights(flights: Flight[]): Flight[]`
- Removes duplicate flights using composite keys
- Logs deduplication statistics
- Preserves first occurrence of duplicates

### `sortFlightsByPrice(flights: Flight[]): Flight[]`
- Primary sort: Price (lowest first)
- Secondary sort: Duration (shortest first)
- Returns new sorted array

### `buildFilterOptions(flights: Flight[]): FlightFilterOptions`
- Extracts unique airlines
- Calculates actual price and duration ranges
- Builds comprehensive filter options

### `applyFilters(flights: Flight[], activeFilters: ActiveFilters): Flight[]`
- Applies user-selected filters
- Supports multiple filter types simultaneously
- Returns filtered flight list

### `paginateFlights(flights: Flight[], page: number, pageSize: number): PaginatedFlights`
- Splits flights into pages (default: 20 per page)
- Returns pagination metadata
- Handles edge cases (empty lists, invalid page numbers)

## Main Processing Function

```typescript
export const processFlightList = (
  flightList: Flight[],
  isMaster: boolean,
  currentMasterState?: MasterFlightListState,
  activeFilters?: ActiveFilters,
  currentPage: number = 1,
  pageSize: number = 20
): {
  masterState: MasterFlightListState;
  filteredFlights?: Flight[];
  paginatedResult?: PaginatedFlights;
  shouldApplyFilters: boolean;
}
```

### Return Values:

- **`masterState`**: Updated master flight list state
- **`filteredFlights`**: Filtered flights (only for Case A)
- **`paginatedResult`**: Paginated flights (only for Case A)
- **`shouldApplyFilters`**: Boolean indicating if UI should reapply filters

## Edge Cases Handled

1. **Empty Flight Lists**
   - Returns empty but valid state
   - Maintains consistent structure

2. **Invalid Flight Data**
   - Normalizes malformed flights
   - Applies fallback values
   - Logs errors for debugging

3. **Missing Prices**
   - Sets price to 0 for invalid prices
   - Excludes from price range calculations
   - Maintains sort stability

4. **Duplicate Flights**
   - Comprehensive deduplication logic
   - Multiple key strategies
   - Detailed logging

5. **Network Failures**
   - Graceful error handling
   - Fallback to previous state
   - Error reporting

## Usage Examples

### First Provider Response
```typescript
const result = processFlightList(
  providerFlights,
  false, // First provider
  undefined, // No existing state
  activeFilters,
  1, // Page 1
  20 // 20 per page
);

// result.paginatedResult.flights contains flights to display
// result.masterState contains updated master list
// result.shouldApplyFilters is false (already applied)
```

### Subsequent Provider Response
```typescript
const result = processFlightList(
  newProviderFlights,
  true, // Subsequent provider
  existingMasterState,
  undefined, // Don't apply filters here
  1,
  20
);

// result.masterState contains merged and updated master list
// result.shouldApplyFilters is true
// UI should now reapply active filters to updated master list
```

## Integration Notes

- The system is designed to work with existing React state management
- Filter options are automatically updated when new providers are added
- Pagination state should be reset when new providers are processed
- The UI should handle the `shouldApplyFilters` flag appropriately
