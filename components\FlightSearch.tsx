
'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import AirportSelector from './AirportSelector';
import FlightCalendar from './FlightCalendar';
import TravelersClassSelector from './TravelersClassSelector';
import { AirportSelectorData } from '../app/api/services/airportService';

const serviceCards = [
  {
    id: 'flights',
    icon: 'ri-plane-line',
    title: 'Flights',
    active: true,
    disabled: false
  },
  {
    id: 'hotels',
    icon: 'ri-building-line',
    title: 'Hotels',
    disabled: false
  },
  {
    id: 'holidays',
    icon: 'ri-map-line',
    title: 'Holiday',
    disabled: false
  }
];

const defaultAirports: AirportSelectorData[] = [
  // Indian Airports
  { code: 'DEL', name: 'Delhi', fullName: 'DEL, Delhi Airport India' },
  { code: 'BLR', name: 'Bengaluru', fullName: 'BLR, Bengaluru International Airport' },
  { code: 'BOM', name: 'Mumbai', fullName: 'BOM, Chhatrapati Shivaji Mumbai International Airport' },
  { code: 'MAA', name: 'Chennai', fullName: 'MAA, Chennai International Airport' },
  { code: 'CCU', name: 'Kolkata', fullName: 'CCU, Netaji Subhash Chandra Bose International Airport' },
  { code: 'HYD', name: 'Hyderabad', fullName: 'HYD, Rajiv Gandhi International Airport' },
  { code: 'AMD', name: 'Ahmedabad', fullName: 'AMD, Sardar Vallabhbhai Patel International Airport' },
  { code: 'PNQ', name: 'Pune', fullName: 'PNQ, Pune Airport' },
  { code: 'GOI', name: 'Goa', fullName: 'GOI, Goa International Airport' },
  { code: 'JAI', name: 'Jaipur', fullName: 'JAI, Jaipur International Airport' },
  { code: 'COK', name: 'Kochi', fullName: 'COK, Cochin International Airport' },
  { code: 'TRV', name: 'Thiruvananthapuram', fullName: 'TRV, Trivandrum International Airport' },
  { code: 'CJB', name: 'Coimbatore', fullName: 'CJB, Coimbatore International Airport' },
  { code: 'NAG', name: 'Nagpur', fullName: 'NAG, Dr. Babasaheb Ambedkar International Airport' },
  { code: 'BBI', name: 'Bhubaneswar', fullName: 'BBI, Biju Patnaik International Airport' },
  { code: 'GAU', name: 'Guwahati', fullName: 'GAU, Lokpriya Gopinath Bordoloi International Airport' },
  { code: 'IDR', name: 'Indore', fullName: 'IDR, Devi Ahilya Bai Holkar Airport' },
  { code: 'BHO', name: 'Bhopal', fullName: 'BHO, Raja Bhoj Airport' },

  // US Airports - Chicago & New York
  { code: 'ORD', name: 'Chicago', fullName: 'ORD, Chicago O\'Hare International Airport' },
  { code: 'MDW', name: 'Chicago', fullName: 'MDW, Chicago Midway International Airport' },
  { code: 'JFK', name: 'New York', fullName: 'JFK, John F. Kennedy International Airport' },
  { code: 'LGA', name: 'New York', fullName: 'LGA, LaGuardia Airport' },
  { code: 'EWR', name: 'New York', fullName: 'EWR, Newark Liberty International Airport' },

  // Other Major US Airports
  { code: 'LAX', name: 'Los Angeles', fullName: 'LAX, Los Angeles International Airport' },
  { code: 'MIA', name: 'Miami', fullName: 'MIA, Miami International Airport' },
  { code: 'SFO', name: 'San Francisco', fullName: 'SFO, San Francisco International Airport' },
  { code: 'ATL', name: 'Atlanta', fullName: 'ATL, Hartsfield-Jackson Atlanta International Airport' },
  { code: 'DFW', name: 'Dallas', fullName: 'DFW, Dallas/Fort Worth International Airport' },

  // International Airports
  { code: 'DXB', name: 'Dubai', fullName: 'DXB, Dubai International Airport' },
  { code: 'SIN', name: 'Singapore', fullName: 'SIN, Singapore Changi Airport' },
  { code: 'LHR', name: 'London', fullName: 'LHR, London Heathrow Airport' }
];

export default function FlightSearch({ isModify = false }) {
  const router = useRouter();
  const [activeService, setActiveService] = useState('flights');
  const [tripType, setTripType] = useState('oneWay');
  const [fareType, setFareType] = useState('regular');

  const [showCalendar, setShowCalendar] = useState(false);
  const [dateSelectionStep, setDateSelectionStep] = useState('departure');
  const [showTravelersClass, setShowTravelersClass] = useState(false);
  const [selectedFromAirport, setSelectedFromAirport] = useState(defaultAirports[0]);
  const [selectedToAirport, setSelectedToAirport] = useState(defaultAirports[1]);
  const [departureDate, setDepartureDate] = useState({ day: '31', month: 'Jul\'25', dayName: 'Thursday' });
  const [returnDate, setReturnDate] = useState({ day: '01', month: 'Aug\'25', dayName: 'Friday' });
  const [currentMonth, setCurrentMonth] = useState(0); // 0: Jul, 1: Aug, 2: Sep
  const [travelers, setTravelers] = useState({ adults: 1, children: 0, infants: 0 });
  const [selectedClass, setSelectedClass] = useState('economy');

  const months = [
    { name: 'July 2025', short: 'Jul\'25', index: 0 },
    { name: 'August 2025', short: 'Aug\'25', index: 1 },
    { name: 'September 2025', short: 'Sep\'25', index: 2 }
  ];

  const generateCalendarDays = (monthIndex) => {
    const days = [];
    const daysInMonth = [31, 31, 30][monthIndex]; // Jul, Aug, Sep
    const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

    for (let day = 1; day <= daysInMonth; day++) {
      const dayName = dayNames[Math.floor(Math.random() * dayNames.length)];
      const basePrice = 2000 + Math.floor(Math.random() * 3000);
      const discount = Math.floor(Math.random() * 500);
      const price = basePrice - discount;

      days.push({
        day: day.toString().padStart(2, '0'),
        month: months[monthIndex].short,
        monthName: months[monthIndex].name,
        dayName,
        price,
        isLowestPrice: Math.random() > 0.8 // 20% chance of being lowest price
      });
    }
    return days;
  };



  // Calendar click handlers
  const handleDepartureClick = () => {
    setShowCalendar(true);
    setDateSelectionStep('departure');
  };

  const handleReturnClick = () => {
    if (tripType === 'oneWay') {
      setTripType('roundTrip');
    }
    setShowCalendar(true);
    setDateSelectionStep('return');
  };

  // Enhanced date selection handler
  const handleDateSelect = (dateInfo) => {
    if (tripType === 'oneWay') {
      setDepartureDate(dateInfo);
      setShowCalendar(false);
    } else {
      // Round trip logic
      if (dateSelectionStep === 'departure') {
        setDepartureDate(dateInfo);
        setDateSelectionStep('return');
      } else {
        setReturnDate(dateInfo);
        setShowCalendar(false);
        setDateSelectionStep('departure');
      }
    }
  };

  const swapAirports = () => {
    const temp = selectedFromAirport;
    setSelectedFromAirport(selectedToAirport);
    setSelectedToAirport(temp);
  };

  const getReturnDate = () => {
    if (tripType === 'roundTrip') {
      return returnDate;
    }
    return null;
  };

  const nextMonth = () => {
    setCurrentMonth(prev => (prev < 2 ? prev + 1 : prev));
  };

  const prevMonth = () => {
    setCurrentMonth(prev => (prev > 0 ? prev - 1 : prev));
  };

  // Check if date is in selected range
  const isDateInRange = (dateInfo) => {
    if (tripType !== 'roundTrip' || dateSelectionStep === 'departure') return false;

    const depDate = parseInt(departureDate.day);
    const currentDate = parseInt(dateInfo.day);
    const retDate = parseInt(returnDate.day);

    return currentDate >= depDate && currentDate <= Math.max(depDate, retDate);
  };

  // Check if date is selected
  const isSelectedDate = (dateInfo) => {
    const currentDate = parseInt(dateInfo.day);
    const depDate = parseInt(departureDate.day);
    const retDate = parseInt(returnDate.day);

    if (tripType === 'roundTrip') {
      return currentDate === depDate || currentDate === retDate;
    }
    return currentDate === depDate;
  };

  const handleTravelerChange = (type, operation) => {
    setTravelers(prev => {
      const newCount = operation === 'increment'
        ? Math.min(prev[type] + 1, type === 'adults' ? 9 : type === 'children' ? 8 : 2)
        : Math.max(prev[type] - 1, type === 'adults' ? 1 : 0);

      return { ...prev, [type]: newCount };
    });
  };

  const getTotalTravelers = () => {
    return travelers.adults + travelers.children + travelers.infants;
  };

  const getClassDisplay = () => {
    const classNames = {
      economy: 'Economy',
      premiumEconomy: 'Premium Economy',
      business: 'Business',
      first: 'First'
    };
    return classNames[selectedClass] || 'Economy';
  };

  const handleSearchFlights = () => {
    const searchParams = new URLSearchParams({
      from: selectedFromAirport.code, // Use airport code instead of name
      to: selectedToAirport.code, // Use airport code instead of name
      fromName: selectedFromAirport.name, // Keep name for display
      toName: selectedToAirport.name, // Keep name for display
      depart: `${departureDate.day} ${departureDate.month}`,
      ...(tripType === 'roundTrip' && returnDate && {
        return: `${returnDate.day} ${returnDate.month}`
      }),
      travelers: getTotalTravelers().toString(),
      class: selectedClass,
      tripType,
      fareType
    });

    router.push(`/flights?${searchParams.toString()}`);
  };

  // Add useEffect to detect screen size for mobile/desktop
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Add isMobile state
  const [isMobile, setIsMobile] = useState(false);

  return (
    <div className={`${isModify ? '' : 'pt-20 md:pt-32 pb-8 md:pb-16 px-4 md:px-6'}`}>
      <div className="max-w-7xl mx-auto relative">
        {/* Service Cards */}
        {!isModify &&
          <div className="hidden md:flex justify-center gap-2 md:gap-4">
            {serviceCards.map((service) => (
              <button
                key={service.id}
                onClick={() => !service.disabled && setActiveService(service.id)}
                disabled={service.disabled}
                className={`px-3 md:px-4 py-2 md:py-3 rounded-lg text-center transition-all min-w-[100px] md:min-w-[140px] flex-shrink-0 ${activeService === service.id
                  ? 'bg-primary text-white shadow-lg'
                  : service.disabled
                    ? 'bg-gray-100 border border-gray-200 cursor-not-allowed opacity-60'
                    : 'bg-white shadow-md hover:shadow-lg border border-gray-200 hover:scale-105'
                  }`}
              >
                <div
                  className={`w-6 md:w-8 h-6 md:h-8 flex items-center justify-center mx-auto mb-1 md:mb-2 rounded-full ${activeService === service.id
                    ? 'bg-white/20 text-white'
                    : service.disabled
                      ? 'bg-gray-200 text-gray-400'
                      : 'bg-gray-50 text-gray-600'
                    }`}
                >
                  <i className={`${service.icon} text-base md:text-lg`}></i>
                </div>
                <div
                  className={`text-xs md:text-sm font-semibold ${activeService === service.id
                    ? 'text-white'
                    : service.disabled
                      ? 'text-gray-400'
                      : 'text-gray-700'
                    }`}
                >
                  {service.title}
                </div>
              </button>
            ))}
          </div>
        }


        {/* Flight Search Form */}
        <div className={`bg-white rounded-2xl shadow-2xl p-3 md:p-6 ${isModify ? '' : 'md:-mt-8'} md:pt-6 border border-gray-100`}>
          {/* Trip Type Selection */}
          <div className="flex items-center justify-between mb-3 md:mb-4 flex-col md:flex-row gap-3">
            <div className="flex items-center bg-gray-100 rounded-full p-1">
              <button
                onClick={() => setTripType('oneWay')}
                className={`px-3 md:px-5 py-1.5 md:py-2.5 rounded-full text-sm font-semibold transition-all whitespace-nowrap ${tripType === 'oneWay'
                  ? 'bg-[#013688] text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-800'
                  }`}
              >
                One Way
              </button>
              <button
                onClick={() => setTripType('roundTrip')}
                className={`px-3 md:px-5 py-1.5 md:py-2.5 rounded-full text-sm font-semibold transition-all whitespace-nowrap ${tripType === 'roundTrip'
                  ? 'bg-[#013688] text-white shadow-md'
                  : 'text-gray-600 hover:text-gray-800'
                  }`}
              >
                Round Trip
              </button>
            </div>
            <div className="text-center md:text-right">
              <div className="text-xs md:text-sm font-medium text-gray-700 flex items-center justify-center md:justify-end">
                <div className="w-4 h-4 flex items-center justify-center mr-2">
                  <i className="ri-global-line text-[#013688]"></i>
                </div>
                  <span className='font-bold'>Book International and Domestic Flights</span>
              </div>
            </div>
          </div>

          {/* Mobile Layout - Stack vertically */}
          <div className="block md:hidden space-y-3 mb-4">
            {/* From */}
            <AirportSelector
              selectedAirport={selectedFromAirport}
              onAirportSelect={(airport) => setSelectedFromAirport(airport)}
              label="From"
              placeholder="Search departure airports..."
              isMobile={true}
              useMobileBottomSheet={true}
            />

            {/* Swap Button */}
            <div className="relative">
              <button
                onClick={swapAirports}
                className="p-3 text-primary hover:bg-blue-50 rounded-full border-2 border-gray-200 hover:border-primary transition-all bg-white absolute top-0 right-0 -translate-y-1/2"
              >
                <div className="w-5 h-5 flex items-center justify-center">
                  <i className="ri-arrow-up-down-line text-lg"></i>
                </div>
              </button>
            </div>

            {/* To */}
            <AirportSelector
              selectedAirport={selectedToAirport}
              onAirportSelect={(airport) => setSelectedToAirport(airport)}
              label="To"
              placeholder="Search destination airports..."
              isMobile={true}
              useMobileBottomSheet={true}
            />

            {/* Dates */}
            <div className="grid grid-cols-2 gap-3">
              {/* Departure */}
              <div className="relative p-2.5 border border-gray-300 rounded-lg cursor-pointer hover:border-[#013688] transition-all bg-white min-h-[55px]">
                <label className="block text-sm font-medium text-gray-600">Departure</label>
                <button
                  onClick={handleDepartureClick}
                  className="w-full flex flex-col justify-center text-left"
                >
                  <div className="font-bold text-lg text-gray-900">{departureDate.day}</div>
                  <div className="text-xs text-gray-500">{departureDate.month}</div>
                </button>
              </div>

              {/* Return */}
              <div className="relative p-2.5 border border-gray-300 rounded-lg cursor-pointer hover:border-[#013688] transition-all bg-white min-h-[55px]">
                <label className="block text-sm font-medium text-gray-600">Return</label>
                <button
                  onClick={handleReturnClick}
                  className="w-full flex flex-col justify-center text-left"
                >
                  {tripType === 'oneWay' ? (
                    <div className="text-center">
                      <div className="font-medium text-gray-400 text-sm">Add return</div>
                    </div>
                  ) : (
                    <div>
                      <div className="font-bold text-lg text-gray-900">{getReturnDate()?.day}</div>
                      <div className="text-xs text-gray-500">{getReturnDate()?.month}</div>
                    </div>
                  )}
                </button>
              </div>
            </div>

            {/* Mobile Layout - Travellers & Class */}
            <div className='p-2 border border-gray-300 rounded-lg text-left hover:border-[#013688] focus:border-[#013688] focus:outline-none transition-all bg-white min-h-[50px]'>
              <label className="block text-sm font-medium text-gray-600">Travellers & Class</label>
              <div className="relative">
                <button
                  onClick={() => setShowTravelersClass(!showTravelersClass)}
                  className="w-full flex flex-col justify-center text-left"
                >
                  <div className="font-bold text-base text-gray-900">{getTotalTravelers()} <span className='text-xs text-gray-500'>Traveller{getTotalTravelers() > 1 ? 's' : ''}</span></div>
                  <div className="text-xs text-gray-500">{getClassDisplay()}</div>
                </button>
              </div>
            </div>

          </div>

          {/* Desktop Layout - Single line */}
          <div className="hidden md:flex items-end mb-5 relative border border-gray-200 rounded-2xl">
            {/* From */}
            <div className="w-1/5 relative p-3 border-r border-gray-200">
              <AirportSelector
                selectedAirport={selectedFromAirport}
                onAirportSelect={(airport) => setSelectedFromAirport(airport)}
                label="From"
                className="relative"
              />
              <div className='absolute right-0 translate-x-1/2 top-1/2 transform -translate-y-1/2'>
                <button
                  onClick={swapAirports}
                  className="p-1 text-primary bg-white hover:bg-blue-50 rounded-full border-2 border-gray-200 hover:border-primary transition-all flex items-center justify-center"
                >
                  <div className="w-6 h-6 flex items-center justify-center">
                    <i className="ri-arrow-left-right-line text-xl"></i>
                  </div>
                </button>
              </div>
            </div>

            {/* To */}
            <div className="w-1/5 relative p-3 pl-5 border-r border-gray-200">
              <AirportSelector
                selectedAirport={selectedToAirport}
                onAirportSelect={(airport) => setSelectedToAirport(airport)}
                label="To"
              />
            </div>

            {/* Departure */}
            <div className="w-1/5 relative p-3 border-r border-gray-200">
              <label className="block text-sm font-medium text-gray-600">Departure</label>
              <button
                onClick={handleDepartureClick}
                className="w-full text-left"
              >
                <div className="font-bold text-xl text-gray-900">{departureDate.day}</div>
                <div className="text-sm text-gray-500">{departureDate.month}, {departureDate.dayName}</div>
              </button>
            </div>

            {/* Return */}
            <div className="w-1/5 relative p-3 border-r border-gray-200">
              <label className="block text-sm font-medium text-gray-600">Return</label>
              <button
                onClick={handleReturnClick}
                className="w-full text-left"
              >
                {tripType === 'oneWay' ? (
                  <div>
                    <div className="font-medium text-gray-400">Tap to add a return</div>
                    <div className="text-sm text-gray-400">date for bigger discounts</div>
                  </div>
                ) : (
                  <div>
                    <div className="font-bold text-xl text-gray-900">{returnDate?.day}</div>
                    <div className="text-sm text-gray-500">{returnDate?.month}, {returnDate?.dayName}</div>
                  </div>
                )}
              </button>
            </div>

            {/* Desktop Layout - Travellers & Class */}
            <div className="w-1/5 relative p-3">
              <label className="block text-sm font-medium text-gray-600">Travellers & Class</label>
              <div className="relative">
                <button
                  onClick={() => setShowTravelersClass(!showTravelersClass)}
                  className="w-full text-left"
                >
                  <div className="font-bold text-lg text-gray-900">{getTotalTravelers()}</div>
                  <div className="text-sm text-gray-500">{getClassDisplay()}</div>
                </button>
                <TravelersClassSelector
                  isOpen={showTravelersClass}
                  onClose={() => setShowTravelersClass(false)}
                  travelers={travelers}
                  onTravelersChange={setTravelers}
                  selectedClass={selectedClass}
                  onClassChange={setSelectedClass}
                  isMobile={isMobile}
                />
              </div>
            </div>

          </div>

          {/* Travelers & Class Selector Component */}


          {/* Enhanced Calendar Modal */}
          <FlightCalendar
            isOpen={showCalendar}
            onClose={() => {
              setShowCalendar(false);
              setDateSelectionStep('departure');
            }}
            onDateSelect={handleDateSelect}
            tripType={tripType}
            selectedDepartureDate={departureDate}
            selectedReturnDate={returnDate}
            dateSelectionStep={dateSelectionStep}
          />

          {/* Special Fare Selection */}
          <div className="mb-4 md:mb-5">
            <div className="text-sm font-semibold text-gray-800 mb-3">Select a special fare</div>
            <div className="flex items-center flex-wrap gap-2 md:gap-3">
              <div className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-xs font-bold">EXTRA SAVINGS</div>

              <label className="flex items-center space-x-2 md:space-x-3 cursor-pointer p-1.5 md:p-2.5 rounded-lg hover:bg-gray-50 transition-all flex-shrink-0">
                <input
                  type="radio"
                  className="accent-[#013688] w-4 h-4"
                  name="fareType"
                  checked={fareType === 'regular'}
                  onChange={() => setFareType('regular')}
                />
                <div>
                  <div className="font-semibold text-gray-800 text-sm">Regular</div>
                  <div className="text-xs text-gray-500">Regular fares</div>
                </div>
              </label>

              <label className="flex items-center space-x-2 md:space-x-3 cursor-pointer p-1.5 md:p-2.5 rounded-lg hover:bg-gray-50 transition-all flex-shrink-0">
                <input
                  type="radio"
                  className="accent-[#013688] w-4 h-4"
                  name="fareType"
                  checked={fareType === 'student'}
                  onChange={() => setFareType('student')}
                />
                <div>
                  <div className="font-semibold text-gray-800 text-sm">Student</div>
                  <div className="text-xs text-gray-500 hidden md:block">Extra discounts/baggage</div>
                </div>
              </label>

              <label className="flex items-center space-x-2 md:space-x-3 cursor-pointer p-1.5 md:p-2.5 rounded-lg hover:bg-gray-50 transition-all flex-shrink-0">
                <input
                  type="radio"
                  className="accent-[#013688] w-4 h-4"
                  name="fareType"
                  checked={fareType === 'senior'}
                  onChange={() => setFareType('senior')}
                />
                <div>
                  <div className="font-semibold text-gray-800 text-sm">Senior Citizen</div>
                  <div className="text-xs text-gray-500 hidden md:block">Up to ₹ 900 off</div>
                </div>
              </label>

              <label className="flex items-center space-x-2 md:space-x-3 cursor-pointer p-1.5 md:p-2.5 rounded-lg hover:bg-gray-50 transition-all flex-shrink-0">
                <input
                  type="radio"
                  className="accent-[#013688] w-4 h-4"
                  name="fareType"
                  checked={fareType === 'armed'}
                  onChange={() => setFareType('armed')}
                />
                <div>
                  <div className="font-semibold text-gray-800 text-sm">Armed Forces</div>
                  <div className="text-xs text-gray-500 hidden md:block">Up to ₹ 600 off</div>
                </div>
              </label>

              <label className="flex items-center space-x-2 md:space-x-3 cursor-pointer p-1.5 md:p-2.5 rounded-lg hover:bg-gray-50 transition-all flex-shrink-0">
                <input
                  type="radio"
                  className="accent-[#013688] w-4 h-4"
                  name="fareType"
                  checked={fareType === 'doctor'}
                  onChange={() => setFareType('doctor')}
                />
                <div>
                  <div className="font-semibold text-gray-800 text-sm">Doctor and Nurses</div>
                  <div className="text-xs text-gray-500 hidden md:block">Up to ₹ 200 off</div>
                </div>
              </label>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="flex items-center justify-between pt-4 md:pt-6 border-t border-gray-100 mb-4 md:mb-6 flex-col md:flex-row gap-4">
            <div className="flex items-center space-x-2 md:space-x-3 text-center md:text-left">
              <input type="checkbox" className="accent-[#013688] w-4 h-4" />
              <span className="text-sm font-semibold">Add FlexiFly</span>
              <span className="text-xs text-gray-500 hidden md:inline">100% refund on cancellation or Zero date change charges</span>
              <button className="text-[#013688] text-xs underline font-medium hover:text-blue-800">View Details</button>
            </div>
          </div>

          {/* Search Button */}
          <div className="flex justify-center mt-4">
            <button
              onClick={handleSearchFlights}
              className="bg-gradient-to-r from-[#013688] to-blue-700 text-white px-6 md:px-12 py-2.5 md:py-3.5 rounded-lg text-base md:text-lg font-bold whitespace-nowrap hover:from-blue-700 hover:to-[#013688] transition-all shadow-lg hover:shadow-xl w-full md:w-auto"
            >
              SEARCH FLIGHTS
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
