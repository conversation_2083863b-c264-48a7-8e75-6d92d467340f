/**
 * Preview Page Services
 * Handles all booking preview, fare breakdown, and booking-related functionality
 */

import api from './axiosinstance';

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

export interface PreviewData {
  TUI: string;
  Code: string;
  Completed: boolean;
  Msg: string[];
  From: string;
  To: string;
  DepartureDate: string;
  ReturnDate?: string;
  NetAmount: number;
  TotalAmount: number;
  Currency: string;
  Trips: PreviewTrip[];
  Passengers: PreviewPassenger[];
  FareBreakdown: FareBreakdown;
  BookingDetails: BookingDetails;
  PaymentOptions: PaymentOption[];
  CancellationPolicy: CancellationPolicy;
  BaggageInfo: BaggageInfo;
  SeatSelection?: SeatSelection;
  MealPreferences?: MealPreference[];
  SpecialRequests?: SpecialRequest[];
}

export interface PreviewTrip {
  TripType: string;
  Origin: string;
  Destination: string;
  DepartureDate: string;
  Segments: PreviewSegment[];
  TotalDuration: string;
  Stops: number;
}

export interface PreviewSegment {
  Airline: string;
  FlightNumber: string;
  Aircraft: string;
  Origin: string;
  Destination: string;
  DepartureDateTime: string;
  ArrivalDateTime: string;
  Duration: string;
  Cabin: string;
  FareClass: string;
  Baggage: {
    Cabin: string;
    CheckIn: string;
  };
  Meal: string;
  Entertainment: boolean;
  WiFi: boolean;
}

export interface PreviewPassenger {
  Type: 'Adult' | 'Child' | 'Infant';
  Title: string;
  FirstName: string;
  LastName: string;
  DateOfBirth?: string;
  PassportNumber?: string;
  PassportExpiry?: string;
  Nationality?: string;
  SpecialRequests?: string[];
}

export interface FareBreakdown {
  BaseFare: number;
  Taxes: number;
  Fees: number;
  Surcharges: number;
  Discounts: number;
  TotalAmount: number;
  Currency: string;
  PerPassenger: {
    Adult: number;
    Child: number;
    Infant: number;
  };
}

export interface BookingDetails {
  BookingReference?: string;
  PNR?: string;
  BookingStatus: 'PENDING' | 'CONFIRMED' | 'CANCELLED';
  BookingDate: string;
  TimeLimit: string;
  ContactDetails: {
    Email: string;
    Phone: string;
    Address?: string;
  };
}

export interface PaymentOption {
  Type: 'CREDIT_CARD' | 'DEBIT_CARD' | 'NET_BANKING' | 'UPI' | 'WALLET';
  Name: string;
  Icon: string;
  ProcessingFee: number;
  Currency: string;
  Available: boolean;
}

export interface CancellationPolicy {
  Refundable: boolean;
  CancellationFee: number;
  Currency: string;
  TimeLimit: string;
  Conditions: string[];
}

export interface BaggageInfo {
  Cabin: {
    Weight: string;
    Dimensions: string;
    Pieces: number;
  };
  CheckIn: {
    Weight: string;
    Pieces: number;
    ExcessFee: number;
    Currency: string;
  };
  SpecialBaggage?: {
    SportsEquipment: boolean;
    MusicalInstruments: boolean;
    Fee: number;
    Currency: string;
  };
}

export interface SeatSelection {
  Available: boolean;
  SeatMap: SeatMap[];
  Fees: SeatFee[];
}

export interface SeatMap {
  Row: number;
  Seats: Seat[];
}

export interface Seat {
  Number: string;
  Type: 'WINDOW' | 'AISLE' | 'MIDDLE';
  Status: 'AVAILABLE' | 'OCCUPIED' | 'BLOCKED';
  Fee: number;
  Currency: string;
}

export interface SeatFee {
  Type: 'STANDARD' | 'PREFERRED' | 'EXTRA_LEGROOM' | 'EMERGENCY_EXIT';
  Fee: number;
  Currency: string;
}

export interface MealPreference {
  Code: string;
  Name: string;
  Description: string;
  Fee: number;
  Currency: string;
  Available: boolean;
}

export interface SpecialRequest {
  Code: string;
  Name: string;
  Description: string;
  Fee: number;
  Currency: string;
  RequiresApproval: boolean;
}

export interface PreviewResponse {
  success: boolean;
  message: string;
  data?: PreviewData;
}

export interface PreviewRequest {
  ClientID: string;
  Mode: string;
  Options: string;
  Source: string;
  TripType: string;
  Provider: string;
  Trips: Array<{
    Origin: string;
    Destination: string;
    DepartureDate: string;
  }>;
  Amount: number;
  Index: number;
  OrderID?: string;
  TUI: string;
  priceIds?: string[];
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

// Load dummy preview data from JSON file
export const loadDummyPreviewData = async (): Promise<PreviewData> => {
  try {
    console.log('📁 Loading dummy preview data from JSON file...');

    const response = await fetch('/assets/json/dummydata/previewdummy.json');
    if (!response.ok) {
      throw new Error(`Failed to load JSON file: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ Dummy preview data loaded successfully');

    return data;
  } catch (error) {
    console.error('❌ Error loading dummy preview data:', error);

    // Return fallback data
    return {
      TUI: 'FALLBACK_TUI',
      Code: '200',
      Completed: true,
      Msg: ['Fallback preview data loaded'],
      From: 'DEL',
      To: 'BOM',
      DepartureDate: new Date().toISOString().split('T')[0],
      NetAmount: 5000,
      TotalAmount: 6000,
      Currency: 'INR',
      Trips: [],
      Passengers: [],
      FareBreakdown: {
        BaseFare: 5000,
        Taxes: 800,
        Fees: 200,
        Surcharges: 0,
        Discounts: 0,
        TotalAmount: 6000,
        Currency: 'INR',
        PerPassenger: {
          Adult: 6000,
          Child: 4500,
          Infant: 1000
        }
      },
      BookingDetails: {
        BookingStatus: 'PENDING',
        BookingDate: new Date().toISOString(),
        TimeLimit: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        ContactDetails: {
          Email: '',
          Phone: ''
        }
      },
      PaymentOptions: [],
      CancellationPolicy: {
        Refundable: false,
        CancellationFee: 1000,
        Currency: 'INR',
        TimeLimit: '24 hours',
        Conditions: ['Standard cancellation policy applies']
      },
      BaggageInfo: {
        Cabin: {
          Weight: '7 kg',
          Dimensions: '55x35x25 cm',
          Pieces: 1
        },
        CheckIn: {
          Weight: '15 kg',
          Pieces: 1,
          ExcessFee: 500,
          Currency: 'INR'
        }
      }
    };
  }
};

// Transform preview data for booking page
export const transformPreviewDataForBooking = (previewData: PreviewData, flightData?: any) => {
  return {
    ...previewData,
    // Merge with flight data if provided
    ...(flightData && {
      From: flightData.departure?.airport || previewData.From,
      To: flightData.arrival?.airport || previewData.To,
      DepartureDate: flightData.departure?.date || previewData.DepartureDate
    })
  };
};

// Calculate fare breakdown
export const calculateFareBreakdown = (basePrice: number, passengers: { adults: number; children: number; infants: number }) => {
  const adultPrice = basePrice;
  const childPrice = basePrice * 0.75; // 25% discount for children
  const infantPrice = basePrice * 0.1; // 90% discount for infants

  const baseFare = (adultPrice * passengers.adults) + (childPrice * passengers.children) + (infantPrice * passengers.infants);
  const taxes = baseFare * 0.16; // 16% taxes
  const fees = 200; // Fixed fees
  const surcharges = 0;
  const discounts = 0;

  const totalAmount = baseFare + taxes + fees + surcharges - discounts;

  return {
    BaseFare: Math.round(baseFare),
    Taxes: Math.round(taxes),
    Fees: fees,
    Surcharges: surcharges,
    Discounts: discounts,
    TotalAmount: Math.round(totalAmount),
    Currency: 'INR',
    PerPassenger: {
      Adult: Math.round(adultPrice + (adultPrice * 0.16) + (fees / passengers.adults)),
      Child: Math.round(childPrice + (childPrice * 0.16)),
      Infant: Math.round(infantPrice + (infantPrice * 0.16))
    }
  };
};

// ============================================================================
// MAIN PREVIEW PAGE SERVICE
// ============================================================================

export const previewPageService = {
  // Get preview data by TUI
  async getPreviewData(tui?: string, fromCode?: string, toCode?: string): Promise<PreviewResponse> {
    try {
      console.log('🚀 Getting preview data:', { tui, fromCode, toCode });

      if (tui) {
        // Try to get specific TUI data from API
        try {
          const response = await api.get(`/api/v1/preview/${tui}`);
          if (response.data) {
            console.log('✅ Preview data retrieved from API');
            return {
              success: true,
              message: 'Preview data retrieved successfully',
              data: response.data
            };
          }
        } catch (apiError) {
          console.warn('⚠️ API preview failed, falling back to JSON data');
        }
      }

      // Fallback to JSON data
      const jsonData = await loadDummyPreviewData();

      // Update data with provided parameters
      const updatedData = {
        ...jsonData,
        ...(tui && { TUI: tui }),
        ...(fromCode && { From: fromCode }),
        ...(toCode && { To: toCode })
      };

      return {
        success: true,
        message: 'Preview data loaded from JSON file',
        data: updatedData
      };
    } catch (error: any) {
      console.error('❌ Failed to get preview data:', error);
      return {
        success: false,
        message: error.message || 'Failed to get preview data'
      };
    }
  },

  // Create preview request
  async createPreviewRequest(previewRequest: PreviewRequest): Promise<PreviewResponse> {
    try {
      console.log('🚀 Creating preview request:', previewRequest);

      const response = await api.post('/api/v1/preview', previewRequest);

      if (response.data) {
        console.log('✅ Preview request created successfully');
        return {
          success: true,
          message: 'Preview request created successfully',
          data: response.data
        };
      } else {
        throw new Error('No data received from preview API');
      }
    } catch (error: any) {
      console.error('❌ Preview request failed:', error);

      // Fallback to JSON data
      try {
        const jsonData = await loadDummyPreviewData();
        const updatedData = {
          ...jsonData,
          TUI: previewRequest.TUI,
          Msg: ['Data loaded from JSON file (API not available)']
        };

        return {
          success: true,
          message: 'Preview data loaded from fallback',
          data: updatedData
        };
      } catch (fallbackError) {
        return {
          success: false,
          message: error.message || 'Preview request failed'
        };
      }
    }
  },

  // Get fare breakdown
  async getFareBreakdown(tui: string, passengers: { adults: number; children: number; infants: number }): Promise<FareBreakdown> {
    try {
      const previewResponse = await this.getPreviewData(tui);

      if (previewResponse.success && previewResponse.data) {
        return previewResponse.data.FareBreakdown;
      } else {
        // Calculate fallback fare breakdown
        const basePrice = 5000; // Default base price
        return calculateFareBreakdown(basePrice, passengers);
      }
    } catch (error) {
      console.error('❌ Error getting fare breakdown:', error);
      // Return fallback fare breakdown
      const basePrice = 5000;
      return calculateFareBreakdown(basePrice, passengers);
    }
  },

  // Get booking details
  async getBookingDetails(tui: string): Promise<BookingDetails | null> {
    try {
      const previewResponse = await this.getPreviewData(tui);

      if (previewResponse.success && previewResponse.data) {
        return previewResponse.data.BookingDetails;
      }

      return null;
    } catch (error) {
      console.error('❌ Error getting booking details:', error);
      return null;
    }
  },

  // Get payment options
  async getPaymentOptions(tui: string): Promise<PaymentOption[]> {
    try {
      const previewResponse = await this.getPreviewData(tui);

      if (previewResponse.success && previewResponse.data) {
        return previewResponse.data.PaymentOptions || [];
      }

      // Return default payment options
      return [
        {
          Type: 'CREDIT_CARD',
          Name: 'Credit Card',
          Icon: '/icons/credit-card.png',
          ProcessingFee: 0,
          Currency: 'INR',
          Available: true
        },
        {
          Type: 'DEBIT_CARD',
          Name: 'Debit Card',
          Icon: '/icons/debit-card.png',
          ProcessingFee: 0,
          Currency: 'INR',
          Available: true
        },
        {
          Type: 'UPI',
          Name: 'UPI',
          Icon: '/icons/upi.png',
          ProcessingFee: 0,
          Currency: 'INR',
          Available: true
        }
      ];
    } catch (error) {
      console.error('❌ Error getting payment options:', error);
      return [];
    }
  },

  // Transform preview data for booking
  transformPreviewDataForBooking,

  // Calculate fare breakdown
  calculateFareBreakdown,

  // Load dummy preview data
  loadDummyPreviewData
};