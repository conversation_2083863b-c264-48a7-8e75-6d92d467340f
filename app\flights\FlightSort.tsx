
'use client';

export default function FlightSort({ sortBy, setSortBy, resultsCount, journeyType = 'Onward' }) {
  const sortOptions = [
    { id: 'price', label: 'Price', icon: 'ri-money-dollar-circle-line' },
    { id: 'duration', label: 'Duration', icon: 'ri-time-line' },
    { id: 'departure', label: 'Departure', icon: 'ri-flight-takeoff-line' },
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
      <div className="flex items-center justify-between flex-wrap gap-4">
        <div className="flex items-center space-x-4">
          <h3 className="font-semibold text-gray-900">
            {resultsCount} {journeyType} Flights Found
          </h3>
          <div className="hidden md:flex items-center space-x-1 text-sm text-gray-600">
            <i className="ri-information-line"></i>
            <span>Prices are per person</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-600">Sort by:</span>
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1">
            {sortOptions.map((option) => (
              <button
                key={option.id}
                onClick={() => setSortBy(option.id)}
                className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-all whitespace-nowrap ${
                  sortBy === option.id
                    ? 'bg-[#013688] text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-200'
                }`}
              >
                <div className="w-4 h-4 flex items-center justify-center">
                  <i className={`${option.icon} text-sm`}></i>
                </div>
                <span className="hidden sm:inline">{option.label}</span>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
