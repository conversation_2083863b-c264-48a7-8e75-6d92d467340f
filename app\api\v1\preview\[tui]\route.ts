import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import { promises as fs } from 'fs';
import path from 'path';

// Backend API configuration
const BACKEND_API_BASE_URL = 'http://192.168.205.163:8080'; // Flight service backend
const API_TIMEOUT = 30000; // 30 seconds

// Create axios instance for backend API calls
const backendApi = axios.create({
  baseURL: BACKEND_API_BASE_URL,
  timeout: API_TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Helper function to load preview data from JSON file
async function loadPreviewDummyData() {
  try {
    console.log('📁 Loading preview data from previewdummy.json file...');

    const filePath = path.join(process.cwd(), 'public', 'assets', 'json', 'dummydata', 'previewdummy.json');
    const fileContents = await fs.readFile(filePath, 'utf8');
    const jsonData = JSON.parse(fileContents);

    console.log('✅ Successfully loaded preview data from JSON file:', {
      TUI: jsonData.TUI,
      Code: jsonData.Code,
      hasTrips: !!jsonData.Trips?.length
    });

    return jsonData;
  } catch (error) {
    console.error('❌ Error loading previewdummy.json:', error);

    // Return minimal fallback if JSON file can't be loaded
    return {
      Code: "200",
      Completed: false,
      Msg: ["Fallback data - JSON file not available"],
      TUI: "fallback-tui"
    };
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { tui: string } }
) {
  const { tui } = params; // Move this outside try block for broader scope

  try {
    const { searchParams } = new URL(request.url);
    
    console.log('🚀 Preview API GET /v1/preview/{tui} called with:', { 
      tui,
      searchParams: Object.fromEntries(searchParams.entries())
    });

    // Validate required parameters
    if (!tui) {
      return NextResponse.json({
        Code: "400",
        Completed: false,
        Msg: ["TUI parameter is required"],
        TUI: null
      }, { status: 400 });
    }

    try {
      // Call backend API - GET /v1/preview/{tui}
      const backendResponse = await backendApi.get(`/v1/preview/${tui}`, {
        params: Object.fromEntries(searchParams.entries())
      });

      console.log('✅ Backend API GET response received:', {
        status: backendResponse.status,
        hasData: !!backendResponse.data
      });

      // Return the backend response directly
      return NextResponse.json(backendResponse.data);

    } catch (backendError: any) {
      console.error('❌ Backend API GET error:', {
        message: backendError.message,
        status: backendError.response?.status,
        statusText: backendError.response?.statusText,
        data: backendError.response?.data
      });

      console.log('⚠️ Backend API failed, falling back to previewdummy.json file');

      // Load data from JSON file as fallback
      const jsonData = await loadPreviewDummyData();

      // Update the TUI in the JSON data to match the requested TUI
      const responseData = {
        ...jsonData,
        TUI: tui,
        Msg: ["Data loaded from previewdummy.json (API not available)"]
      };

      return NextResponse.json(responseData);
    }

  } catch (error) {
    console.error('❌ Preview API GET error:', error);

    // Even if there's a general error, try to load from JSON file
    try {
      console.log('🔄 Attempting to load from JSON file due to general error');
      const jsonData = await loadPreviewDummyData();

      const responseData = {
        ...jsonData,
        TUI: tui,
        Msg: ["Data loaded from previewdummy.json (API error occurred)"]
      };

      return NextResponse.json(responseData);
    } catch (jsonError) {
      console.error('❌ Failed to load JSON fallback:', jsonError);

      return NextResponse.json({
        Code: "500",
        Completed: false,
        Msg: ["Internal server error - no fallback data available"],
        TUI: null
      }, { status: 500 });
    }
  }
}
