
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '../contexts/AuthContext';
import LoginModal from './LoginModal';

export default function Header() {
  const { user, isAuthenticated } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const [activeService, setActiveService] = useState('flights');
  const [showLoginModal, setShowLoginModal] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    if(window.location.pathname === "/"){
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }else{
      setIsScrolled(true);
    }
    
  }, []);

  const serviceNavigation = [
    {
      id: 'flights',
      icon: 'ri-plane-line',
      title: 'Flights',
      href: '/',
      active: true
    },
    {
      id: 'hotels',
      icon: 'ri-building-line',
      title: 'Hotels',
      href: '/hotels',
      disabled: true
    },
    {
      id: 'holidays',
      icon: 'ri-map-line',
      title: 'Holidays',
      href: '/holidays',
      disabled: true
    }
  ];

  return (
    <div className={`fixed top-0 left-0 right-0 z-[9] transition-all duration-300 ${isScrolled ? 'bg-white text-white shadow-md' : 'bg-transparent text-white'
      }`}>
      <div className="px-4 md:px-6 py-2 md:py-3">
        <div className="flex items-center justify-between max-w-7xl mx-auto">
          {/* Left side - Logo */}
          <div className="flex items-center">
            <Link href="/">
              <Image
                src={`${isScrolled ? '/unimoni-s.gif' : '/Unimoni-WIZZ-LOGO-reverse.webp'}`}
                alt="Unimoni Logo"
                width={100}
                height={100}
                className={`transition-all duration-300 ease-in-out cursor-pointer w-auto  ${isScrolled ? 'h-8 md:h-10' : 'h-12 md:h-16'}`}
              />
            </Link>
          </div>

          {/* Center - Service Navigation (Desktop only) */}
          {isScrolled &&
            <div className="hidden md:flex items-center space-x-1">
              {serviceNavigation.map((service) => (
                <Link
                  key={service.id}
                  href={service.disabled ? '#' : service.href}
                  onClick={() => !service.disabled && setActiveService(service.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-semibold transition-all cursor-pointer ${activeService === service.id
                      ? 'text-primary'
                      : service.disabled
                        ? 'text-black cursor-not-allowed'
                        : 'text-black hover:text-white hover:bg-white/10'
                    }`}
                >
                  <div className="w-4 h-4 flex items-center justify-center">
                    <i className={`${service.icon} text-base`}></i>
                  </div>
                  <span className="whitespace-nowrap">{service.title}</span>
                </Link>
              ))}
            </div>
          }

          {/* Mobile menu button */}
          <button
            onClick={() => setShowMobileMenu(!showMobileMenu)}
            className="md:hidden p-2"
          >
            <div className="w-5 h-5 flex items-center justify-center">
              <i className={`ri-${showMobileMenu ? 'close' : 'menu'}-line text-xl`}></i>
            </div>
          </button>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-4 text-sm">
            {/* <div className="relative">
              <button
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
                className={`flex items-center space-x-1 px-2 py-1 rounded transition-colors cursor-pointer ${isScrolled ? 'text-black' : 'text-white'}`}
              >
                <span>English</span>
                <div className="w-4 h-4 flex items-center justify-center">
                  <i className="ri-arrow-down-s-line"></i>
                </div>
              </button>
              {showLanguageDropdown && (
                <div className="absolute top-full right-0 mt-1 bg-white text-black rounded shadow-lg p-2 z-[10000]">
                  <div className="px-3 py-2 hover:bg-gray-100 cursor-pointer">English</div>
                  <div className="px-3 py-2 hover:bg-gray-100 cursor-pointer">हिन्दी</div>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-1 px-2 py-1">
              <span>INR</span>
            </div> */}

            {isAuthenticated ? (
              <Link
                href="/profile"
                className={`flex items-center space-x-2 px-4 py-2 rounded transition-colors cursor-pointer ${isScrolled ? 'text-primary bg-primary bg-opacity-10' : 'bg-white/20 hover:bg-white/30 text-white'}`}
              >
                <div className="w-5 h-5 flex items-center justify-center">
                  {user?.profilePicture ? (
                    <Image
                      src={user.profilePicture}
                      alt="Profile"
                      width={20}
                      height={20}
                      className="rounded-full"
                    />
                  ) : (
                    <i className="ri-user-line"></i>
                  )}
                </div>
                <span className="whitespace-nowrap">Hello {user?.name?.split(' ')[0] || 'User'}</span>
                <div className="w-4 h-4 flex items-center justify-center">
                  <i className="ri-arrow-down-s-line"></i>
                </div>
              </Link>
            ) : (
              <button
                onClick={() => setShowLoginModal(true)}
                className={`flex items-center space-x-2 px-4 py-2 rounded transition-colors cursor-pointer ${isScrolled ? 'text-primary bg-primary bg-opacity-10' : 'bg-white/20 hover:bg-white/30 text-white'}`}
              >
                <div className="w-5 h-5 flex items-center justify-center">
                  <i className="ri-user-line"></i>
                </div>
                <span className="whitespace-nowrap">Login or Create Account</span>
                <div className="w-4 h-4 flex items-center justify-center">
                  <i className="ri-arrow-down-s-line"></i>
                </div>
              </button>
            )}
          </div>

          {/* Mobile Navigation Menu */}
          {showMobileMenu && (
            <div className="absolute top-full left-0 right-0 bg-[#013688] md:hidden border-t border-white/20">
              <div className="px-4 py-4 space-y-4">
                {/* Service Navigation - Mobile */}
                <div className="border-b border-white/20 pb-4">
                  <div className="text-white text-sm font-semibold mb-3">Services</div>
                  <div className="space-y-2">
                    {serviceNavigation.map((service) => (
                      <Link
                        key={service.id}
                        href={service.disabled ? '#' : service.href}
                        onClick={() => {
                          if (!service.disabled) {
                            setActiveService(service.id);
                            setShowMobileMenu(false);
                          }
                        }}
                        className={`flex items-center space-x-3 py-2 px-3 rounded transition-all ${activeService === service.id
                            ? 'bg-white/20 text-white'
                            : service.disabled
                              ? 'text-white/50 cursor-not-allowed'
                              : 'text-white/80 hover:text-white hover:bg-white/10'
                          }`}
                      >
                        <div className="w-5 h-5 flex items-center justify-center">
                          <i className={`${service.icon} text-base`}></i>
                        </div>
                        <span>{service.title}</span>
                        {service.disabled && (
                          <span className="text-xs bg-white/20 px-2 py-1 rounded-full">Soon</span>
                        )}
                      </Link>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between py-2 border-b border-white/20">
                  <span className="text-white">Language</span>
                  <div className="flex items-center space-x-1">
                    <span className="text-white">English</span>
                    <div className="w-4 h-4 flex items-center justify-center">
                      <i className="ri-arrow-down-s-line text-white"></i>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between py-2 border-b border-white/20">
                  <span className="text-white">Currency</span>
                  <span className="text-white">INR</span>
                </div>

                {isAuthenticated ? (
                  <Link
                    href="/profile"
                    onClick={() => setShowMobileMenu(false)}
                    className="w-full flex items-center justify-center space-x-2 bg-white/20 hover:bg-white/30 py-3 rounded text-white transition-colors"
                  >
                    <div className="w-5 h-5 flex items-center justify-center">
                      {user?.profilePicture ? (
                        <Image
                          src={user.profilePicture}
                          alt="Profile"
                          width={20}
                          height={20}
                          className="rounded-full"
                        />
                      ) : (
                        <i className="ri-user-line"></i>
                      )}
                    </div>
                    <span>Hello {user?.name?.split(' ')[0] || 'User'}</span>
                  </Link>
                ) : (
                  <button
                    onClick={() => {
                      setShowLoginModal(true);
                      setShowMobileMenu(false);
                    }}
                    className="w-full flex items-center justify-center space-x-2 bg-white/20 hover:bg-white/30 py-3 rounded text-white transition-colors"
                  >
                    <div className="w-5 h-5 flex items-center justify-center">
                      <i className="ri-user-line"></i>
                    </div>
                    <span>Login or Create Account</span>
                  </button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Login Modal */}
      <LoginModal
        isOpen={showLoginModal}
        onClose={() => setShowLoginModal(false)}
      />
    </div>
  );
}
