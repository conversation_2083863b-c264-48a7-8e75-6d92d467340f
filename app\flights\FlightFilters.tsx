'use client';

import { useState } from 'react';

export default function FlightFilters({ filters, setFilters, flights, isMobile = false, onClose }) {
  const [priceRange, setPriceRange] = useState(filters.priceRange);

  const airlines = [...new Set(flights.map(flight => flight.airline))];
  const minPrice = Math.min(...flights.map(flight => flight.price));
  const maxPrice = Math.max(...flights.map(flight => flight.price));

  const handlePriceChange = (newRange) => {
    setPriceRange(newRange);
    setFilters(prev => ({ ...prev, priceRange: newRange }));
  };

  const handleAirlineToggle = (airline) => {
    setFilters(prev => ({
      ...prev,
      airlines: prev.airlines.includes(airline)
        ? prev.airlines.filter(a => a !== airline)
        : [...prev.airlines, airline]
    }));
  };

  const handleDepartureToggle = (timeSlot) => {
    setFilters(prev => ({
      ...prev,
      departure: prev.departure.includes(timeSlot)
        ? prev.departure.filter(t => t !== timeSlot)
        : [...prev.departure, timeSlot]
    }));
  };

  const clearAllFilters = () => {
    const resetFilters = {
      priceRange: [minPrice, maxPrice],
      airlines: [],
      departure: [],
      arrival: [],
      stops: []
    };
    setFilters(resetFilters);
    setPriceRange(resetFilters.priceRange);
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm ${isMobile ? 'p-0' : 'p-4 md:p-6 sticky top-24'}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        <button
          onClick={clearAllFilters}
          className="text-sm text-[#013688] font-medium hover:text-blue-700 transition-colors"
        >
          Clear All
        </button>
      </div>

      {/* Price Range */}
      <div className="mb-6">
        <h4 className="font-semibold text-gray-900 mb-3">Price Range</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>₹{priceRange[0].toLocaleString()}</span>
            <span>₹{priceRange[1].toLocaleString()}</span>
          </div>
          <div className="relative">
            <input
              type="range"
              min={minPrice}
              max={maxPrice}
              value={priceRange[0]}
              onChange={(e) => handlePriceChange([parseInt(e.target.value), priceRange[1]])}
              className="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <input
              type="range"
              min={minPrice}
              max={maxPrice}
              value={priceRange[1]}
              onChange={(e) => handlePriceChange([priceRange[0], parseInt(e.target.value)])}
              className="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
          </div>
        </div>
      </div>

      {/* Airlines */}
      <div className="mb-6">
        <h4 className="font-semibold text-gray-900 mb-3">Airlines</h4>
        <div className="space-y-2">
          {airlines.map((airline) => {
            const airlineFlights = flights.filter(f => f.airline === airline);
            const minAirlinePrice = Math.min(...airlineFlights.map(f => f.price));
            
            return (
              <label key={airline} className="flex items-center space-x-3 cursor-pointer p-2 hover:bg-gray-50 rounded transition-all">
                <input
                  type="checkbox"
                  checked={filters.airlines.includes(airline)}
                  onChange={() => handleAirlineToggle(airline)}
                  className="accent-[#013688] w-4 h-4"
                />
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-gray-800">{airline}</span>
                    <span className="text-sm text-gray-600">from ₹{minAirlinePrice.toLocaleString()}</span>
                  </div>
                </div>
              </label>
            );
          })}
        </div>
      </div>

      {/* Departure Time */}
      <div className="mb-6">
        <h4 className="font-semibold text-gray-900 mb-3">Departure Time</h4>
        <div className="grid grid-cols-2 gap-2">
          {[
            { id: 'morning', label: 'Morning', icon: 'ri-sun-line', time: '6AM - 12PM' },
            { id: 'afternoon', label: 'Afternoon', icon: 'ri-sun-fill', time: '12PM - 6PM' },
            { id: 'evening', label: 'Evening', icon: 'ri-moon-line', time: '6PM - 12AM' },
            { id: 'night', label: 'Night', icon: 'ri-moon-fill', time: '12AM - 6AM' }
          ].map((timeSlot) => (
            <label key={timeSlot.id} className="cursor-pointer">
              <input
                type="checkbox"
                checked={filters.departure.includes(timeSlot.id)}
                onChange={() => handleDepartureToggle(timeSlot.id)}
                className="sr-only"
              />
              <div className={`p-3 border-2 rounded-lg text-center transition-all ${
                filters.departure.includes(timeSlot.id)
                  ? 'border-[#013688] bg-blue-50 text-[#013688]'
                  : 'border-gray-200 hover:border-gray-300 text-gray-600'
              }`}>
                <div className="w-6 h-6 flex items-center justify-center mx-auto mb-1">
                  <i className={`${timeSlot.icon} text-lg`}></i>
                </div>
                <div className="text-xs font-medium">{timeSlot.label}</div>
                <div className="text-xs opacity-75">{timeSlot.time}</div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Stops */}
      <div className="mb-6">
        <h4 className="font-semibold text-gray-900 mb-3">Stops</h4>
        <div className="space-y-2">
          {['Non-stop', '1 stop', '2+ stops'].map((stop) => (
            <label key={stop} className="flex items-center space-x-3 cursor-pointer p-2 hover:bg-gray-50 rounded transition-all">
              <input
                type="checkbox"
                checked={filters.stops.includes(stop)}
                onChange={() => setFilters(prev => ({
                  ...prev,
                  stops: prev.stops.includes(stop)
                    ? prev.stops.filter(s => s !== stop)
                    : [...prev.stops, stop]
                }))}
                className="accent-[#013688] w-4 h-4"
              />
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <span className="font-medium text-gray-800">{stop}</span>
                  <span className="text-sm text-gray-600">
                    {stop === 'Non-stop' && 'Fastest'}
                    {stop === '1 stop' && 'from ₹3,200'}
                    {stop === '2+ stops' && 'Cheapest'}
                  </span>
                </div>
              </div>
            </label>
          ))}
        </div>
      </div>

      {/* Quick Filters */}
      <div>
        <h4 className="font-semibold text-gray-900 mb-3">Quick Filters</h4>
        <div className="space-y-2">
          <button className="w-full text-left p-2 hover:bg-gray-50 rounded transition-all">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-800">Refundable Fares</span>
              <i className="ri-arrow-right-s-line text-gray-400"></i>
            </div>
          </button>
          <button className="w-full text-left p-2 hover:bg-gray-50 rounded transition-all">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-800">Student Discounts</span>
              <i className="ri-arrow-right-s-line text-gray-400"></i>
            </div>
          </button>
        </div>
      </div>

      {/* Mobile Apply Button */}
      {isMobile && (
        <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4 mt-6">
          <button
            onClick={onClose}
            className="w-full bg-[#013688] text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors"
          >
            Apply Filters
          </button>
        </div>
      )}
    </div>
  );
}
