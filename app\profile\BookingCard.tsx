
'use client';

import Link from 'next/link';

interface BookingCardProps {
  booking: {
    id: string;
    pnr: string;
    type: string;
    status: string;
    airline: string;
    route: string;
    date: string;
    time: string;
    passenger: string;
    amount: string;
    bookingDate: string;
  };
}

export default function BookingCard({ booking }: BookingCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-green-100 text-green-800';
      case 'past':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'ri-time-line';
      case 'past':
        return 'ri-check-line';
      case 'cancelled':
        return 'ri-close-line';
      default:
        return 'ri-information-line';
    }
  };

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-xl p-6 hover:shadow-md transition-all">
      <div className="flex flex-col lg:flex-row lg:items-center justify-between">
        {/* Left Side - Booking Info */}
        <div className="flex-1">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 flex items-center justify-center bg-[#013688] rounded-full">
              <i className="ri-plane-line text-white text-xl"></i>
            </div>
            
            <div>
              <div className="flex items-center space-x-3 mb-1">
                <h3 className="text-lg font-semibold text-gray-900">{booking.airline}</h3>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                  <div className="flex items-center space-x-1">
                    <div className="w-3 h-3 flex items-center justify-center">
                      <i className={getStatusIcon(booking.status)}></i>
                    </div>
                    <span className="capitalize">{booking.status}</span>
                  </div>
                </span>
              </div>
              <p className="text-gray-600">{booking.route}</p>
            </div>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <p className="text-xs text-gray-500 font-medium">PNR Number</p>
              <p className="font-semibold text-gray-900">{booking.pnr}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 font-medium">Travel Date</p>
              <p className="font-semibold text-gray-900">{booking.date}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 font-medium">Time</p>
              <p className="font-semibold text-gray-900">{booking.time}</p>
            </div>
            <div>
              <p className="text-xs text-gray-500 font-medium">Amount</p>
              <p className="font-semibold text-gray-900">{booking.amount}</p>
            </div>
          </div>
        </div>

        {/* Right Side - Actions */}
        <div className="flex flex-col space-y-3 mt-6 lg:mt-0 lg:ml-6 lg:w-48">
          <Link 
            href="/flights/itinerary"
            className="w-full bg-[#013688] text-white py-2 px-4 rounded-lg text-sm font-semibold hover:bg-[#012458] transition-colors cursor-pointer text-center whitespace-nowrap"
          >
            View Details
          </Link>
          
          <div className="flex space-x-2">
            <button className="flex-1 border border-gray-300 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors cursor-pointer whitespace-nowrap">
              <div className="flex items-center justify-center space-x-1">
                <div className="w-4 h-4 flex items-center justify-center">
                  <i className="ri-download-line"></i>
                </div>
                <span>Download</span>
              </div>
            </button>
            
            {booking.status === 'upcoming' && (
              <button className="flex-1 border border-red-300 text-red-600 py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-50 transition-colors cursor-pointer whitespace-nowrap">
                <div className="flex items-center justify-center space-x-1">
                  <div className="w-4 h-4 flex items-center justify-center">
                    <i className="ri-close-line"></i>
                  </div>
                  <span>Cancel</span>
                </div>
              </button>
            )}
          </div>
          
          {booking.status === 'upcoming' && (
            <button className="w-full border border-[#013688] text-[#013688] py-2 px-4 rounded-lg text-sm font-medium hover:bg-[#013688] hover:text-white transition-colors cursor-pointer whitespace-nowrap">
              <div className="flex items-center justify-center space-x-1">
                <div className="w-4 h-4 flex items-center justify-center">
                  <i className="ri-share-line"></i>
                </div>
                <span>Share Itinerary</span>
              </div>
            </button>
          )}
        </div>
      </div>

      {/* Booking Details Footer */}
      <div className="mt-4 pt-4 border-t border-gray-200 flex items-center justify-between text-sm text-gray-500">
        <span>Booking ID: {booking.id}</span>
        <span>Booked on: {booking.bookingDate}</span>
      </div>
    </div>
  );
}
