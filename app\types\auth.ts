// Authentication type definitions

export interface User {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  profilePicture?: string;
  profile_picture?: string; // API compatibility
  isVerified: boolean;
  is_verified?: boolean; // API compatibility
  createdAt: string;
  created_at?: string; // API compatibility
  lastLogin: string;
  last_login?: string; // API compatibility
}

export interface LoginFormData {
  phoneOrEmail: string;
  otp?: string;
  password?: string;
  loginType?: 'email' | 'phone' | 'otp';
}

export interface OTPResponse {
  success: boolean;
  message: string;
  otpId?: string;
  otp_id?: string; // API compatibility
  expiresIn?: number;
  expires_in?: number; // API compatibility
  data?: {
    otp_id?: string;
    expires_in?: number;
    status?: string; // For new API response format
  };
  error?: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  user?: User;
  token?: string;
  data?: {
    user: User;
    access_token: string;
    refresh_token: string;
    token_type: string;
    expires_in: number;
  };
  error?: string;
}

// New API-specific interfaces
export interface LoginApiRequest {
  email?: string;
  phone?: string;
  password?: string;
  otp?: string;
  otp_id?: string; // OTP ID for verification
  login_type?: 'email' | 'phone' | 'otp';
}

export interface LogoutApiRequest {
  refresh_token?: string;
}

export interface LogoutApiResponse {
  success: boolean;
  message: string;
  error?: string;
}

export interface RefreshTokenApiRequest {
  refresh_token: string;
}

export interface RefreshTokenApiResponse {
  success: boolean;
  message: string;
  data?: {
    access_token: string;
    refresh_token?: string;
    token_type: string;
    expires_in: number;
  };
  error?: string;
}

export interface OTPApiRequest {
  phone?: string;
  email?: string;
  type: 'login' | 'register' | 'forgot_password';
}

export interface SocialLoginProvider {
  id: string;
  name: string;
  icon: string;
  color: string;
}

export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (phoneOrEmail: string) => Promise<OTPResponse>;
  loginWithCredentials: (request: LoginApiRequest) => Promise<AuthResponse>;
  verifyOTP: (otpId: string, otp: string) => Promise<AuthResponse>;
  socialLogin: (provider: string) => Promise<AuthResponse>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<RefreshTokenApiResponse>;
  updateUser: (userData: Partial<User>) => void;
}

export interface LoginModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export interface FormErrors {
  phoneOrEmail?: string;
  otp?: string;
  general?: string;
}

export enum LoginStep {
  PHONE_EMAIL = 'phone_email',
  OTP_VERIFICATION = 'otp_verification',
  SUCCESS = 'success'
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

// New API Response Interfaces for localhost:8007
export interface ValidationError {
  loc: (string | number)[];
  msg: string;
  type: string;
}

export interface ValidationErrorResponse {
  detail: ValidationError[];
}

export interface OTPInitiateRequest {
  email?: string;
  phone?: string;
}

export interface OTPInitiateResponse {
  status: string; // Can be "registration_initiated", "login_initiated", etc.
  message: string;
}

export interface OTPVerifyRequest {
  email?: string;
  phone?: string;
  otp: string;
}

export interface OTPVerifyResponse {
  access_token: string;
  token_type: string;
  expires_at: string;
  refresh_token: string;
}

export interface UserProfileResponse {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  profilePicture?: string;
  isVerified: boolean;
  createdAt: string;
  lastLogin: string;
}
