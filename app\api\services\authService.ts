import axios, { AxiosInstance, AxiosError } from 'axios';
import { storageUtils } from '../utils/storageUtils';
import {
  OTPInitiateRequest,
  OTPInitiateResponse,
  OTPVerifyRequest,
  OTPVerifyResponse,
  ValidationErrorResponse,
  OTPResponse,
  AuthResponse,
  RefreshTokenApiResponse
} from '../../../types/auth';

// Create axios instance for auth service
const authApi: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_AUTH_API_BASE_URL || 'http://localhost:8007',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
});

// Add request interceptor to include auth token
authApi.interceptors.request.use(
  (config) => {
    const token = storageUtils.getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

class AuthService {
  /**
   * Initiate O<PERSON> for login
   */
  async sendOTP(request: OTPInitiateRequest): Promise<OTPResponse> {
    try {
      // Validate request
      if (!request.email && !request.phone) {
        return {
          success: false,
          message: 'Email or phone number is required'
        };
      }

      // Validate email format if provided
      if (request.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(request.email)) {
        return {
          success: false,
          message: 'Invalid email format'
        };
      }

      // Validate phone format if provided (basic validation)
      if (request.phone && !/^\+?[\d\s\-\(\)]{10,}$/.test(request.phone)) {
        return {
          success: false,
          message: 'Invalid phone number format'
        };
      }

      const response = await authApi.post<OTPInitiateResponse>('/auth/initiate/otp', request);

      return {
        success: true,
        message: response.data.message || 'OTP sent successfully',
        // Include status information for potential UI handling
        data: {
          status: response.data.status
        }
      };
    } catch (error) {
      console.error('Send OTP error:', error);
      
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError<ValidationErrorResponse>;
        
        // Handle 422 validation errors
        if (axiosError.response?.status === 422 && axiosError.response.data?.detail) {
          const firstError = axiosError.response.data.detail[0];
          return {
            success: false,
            message: firstError.msg || 'Validation error'
          };
        }
        
        // Handle other HTTP errors
        if (axiosError.response?.status) {
          return {
            success: false,
            message: `Server error: ${axiosError.response.status}`
          };
        }
      }

      return {
        success: false,
        message: 'Failed to send OTP. Please try again.'
      };
    }
  }

  /**
   * Verify OTP and login
   */
  async verifyOTP(phoneOrEmail: string, otp: string): Promise<AuthResponse> {
    try {
      // Validate inputs
      if (!phoneOrEmail || !otp) {
        return {
          success: false,
          message: 'Phone/email and OTP are required'
        };
      }

      if (otp.length !== 6) {
        return {
          success: false,
          message: 'OTP must be 6 digits'
        };
      }

      // Determine if input is email or phone
      const isEmail = phoneOrEmail.includes('@');
      const verifyRequest: OTPVerifyRequest = {
        [isEmail ? 'email' : 'phone']: phoneOrEmail,
        otp: otp
      };

      const response = await authApi.post<OTPVerifyResponse>('/auth/verify-otp', verifyRequest);

      // Store tokens
      storageUtils.setAccessToken(response.data.access_token);
      storageUtils.setRefreshToken(response.data.refresh_token);

      // Fetch user profile
      const userProfile = await this.getUserProfile();

      return {
        success: true,
        message: 'Login successful',
        user: userProfile,
        token: response.data.access_token,
        data: {
          user: userProfile,
          access_token: response.data.access_token,
          refresh_token: response.data.refresh_token,
          token_type: response.data.token_type,
          expires_in: this.calculateExpiresIn(response.data.expires_at)
        }
      };
    } catch (error) {
      console.error('Verify OTP error:', error);
      
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError<ValidationErrorResponse>;
        
        // Handle 422 validation errors
        if (axiosError.response?.status === 422 && axiosError.response.data?.detail) {
          const firstError = axiosError.response.data.detail[0];
          return {
            success: false,
            message: firstError.msg || 'Validation error'
          };
        }
        
        // Handle other HTTP errors
        if (axiosError.response?.status) {
          return {
            success: false,
            message: `Verification failed: ${axiosError.response.status}`
          };
        }
      }

      return {
        success: false,
        message: 'Verification failed. Please try again.'
      };
    }
  }

  /**
   * Get user profile
   * TODO: You need to provide the actual user profile endpoint
   * For example: GET /auth/profile or GET /auth/me
   */
  async getUserProfile(): Promise<any> {
    try {
      // TODO: Replace with actual user profile endpoint when you provide it
      // Example implementation:
      // const response = await authApi.get<UserProfileResponse>('/auth/profile');
      // return response.data;

      // For now, return mock user data based on stored email/phone
      const storedIdentifier = localStorage.getItem('temp_login_identifier');
      const isEmail = storedIdentifier?.includes('@');

      return {
        id: `user_${Date.now()}`,
        name: 'User',
        email: isEmail ? storedIdentifier : undefined,
        phone: !isEmail ? storedIdentifier : undefined,
        isVerified: true,
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
      };
    } catch (error) {
      console.error('Get user profile error:', error);
      throw error;
    }
  }

  /**
   * Calculate expires_in from expires_at timestamp
   */
  private calculateExpiresIn(expiresAt: string): number {
    const expirationTime = new Date(expiresAt).getTime();
    const currentTime = Date.now();
    return Math.max(0, Math.floor((expirationTime - currentTime) / 1000));
  }

  /**
   * Refresh access token (placeholder)
   */
  async refreshToken(): Promise<RefreshTokenApiResponse> {
    try {
      const refreshToken = storageUtils.getRefreshToken();
      
      if (!refreshToken) {
        return {
          success: false,
          message: 'No refresh token available'
        };
      }

      // TODO: Implement refresh token endpoint when available
      // const response = await authApi.post('/auth/refresh', { refresh_token: refreshToken });
      
      return {
        success: false,
        message: 'Refresh token endpoint not implemented yet'
      };
    } catch (error) {
      console.error('Refresh token error:', error);
      return {
        success: false,
        message: 'Token refresh failed'
      };
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      // TODO: Call logout endpoint if available
      // await authApi.post('/auth/logout');
      
      // Clear stored data
      storageUtils.clearAuthData();
    } catch (error) {
      console.error('Logout error:', error);
      // Clear data even if API call fails
      storageUtils.clearAuthData();
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return storageUtils.isAuthenticated();
  }

  /**
   * Get current access token
   */
  getAccessToken(): string | null {
    return storageUtils.getAccessToken();
  }

  /**
   * Get current refresh token
   */
  getRefreshToken(): string | null {
    return storageUtils.getRefreshToken();
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;
