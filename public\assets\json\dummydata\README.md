# Flight Dummy Data JSON Files

This directory contains JSON files with dummy flight data for testing and development purposes.

## Files Overview

### Round Trip Data Files

#### `domestic-roundtrip.json`
- **Purpose**: Contains dummy data for domestic round trip flights
- **Routes**: Indian domestic routes (e.g., Delhi ↔ Bengaluru)
- **Airlines**: IndiGo (6E), Air India (AI), Vistara (UK), SpiceJet (SG)
- **Features**: 
  - Multiple fare types (SAVER, FLEXI, ECONOMY, SPICESAVER)
  - Realistic pricing in INR
  - Proper baggage allowances
  - Flight features and amenities
  - Cancellation policies

#### `international-roundtrip.json`
- **Purpose**: Contains dummy data for international round trip flights
- **Routes**: International routes (e.g., Delhi ↔ Dubai)
- **Airlines**: Air India (AI), Emirates (EK), Qatar Airways (QR)
- **Features**:
  - Higher pricing for international flights
  - International baggage allowances (30kg check-in)
  - Premium services and amenities
  - Business class options
  - Connecting flights (for Qatar Airways)

### Legacy Files

#### `flightdetails.json`
- Single flight details for flight details page
- Used by `/api/v1/flights/[id]` endpoint

#### `previewdummy.json`
- Flight booking preview data
- Used for booking confirmation pages

#### `searchlistdummy.json`
- Search list response format
- Used for API response simulation

## Data Structure

### Flight Object Structure
```json
{
  "id": "unique-flight-id",
  "airline": "airline-code",
  "logo": "/AirlineLogo/airline-code.png",
  "flightNumber": "flight-number",
  "segments": [...],
  "departure": {
    "time": "HH:MM",
    "airport": "airport-code",
    "city": "city-name",
    "date": "YYYY-MM-DD",
    "terminal": "terminal-info"
  },
  "arrival": {
    "time": "HH:MM",
    "airport": "airport-code",
    "city": "city-name",
    "date": "YYYY-MM-DD",
    "terminal": "terminal-info"
  },
  "duration": "Xh YYm",
  "stops": "Non-stop" | "1 stop" | "2+ stops",
  "price": number,
  "originalPrice": number,
  "currency": "INR",
  "baggage": {
    "cabin": "X kg",
    "checkin": "Y kg"
  },
  "cancellation": "cancellation-policy",
  "rating": number,
  "features": ["feature1", "feature2"],
  "fares": [...],
  "isRefundable": boolean,
  "fareType": "fare-type"
}
```

## Usage

### Automatic Loading
The system automatically determines whether to load domestic or international data based on the route:

```typescript
// Domestic routes (both airports are Indian)
DEL → BLR  // Loads domestic-roundtrip.json

// International routes (one or both airports are non-Indian)
DEL → DXB  // Loads international-roundtrip.json
```

### Manual Loading
You can also load specific data using the service functions:

```typescript
import { loadDomesticRoundTripData, loadInternationalRoundTripData, loadRoundTripData } from '@/app/api/services/flightService';

// Load specific type
const domesticData = await loadDomesticRoundTripData();
const internationalData = await loadInternationalRoundTripData();

// Auto-detect based on route
const routeData = await loadRoundTripData('DEL', 'DXB');
```

## Adding New Data

### Adding New Flights
1. Choose the appropriate file (domestic or international)
2. Add new flight objects to `onwardFlights` or `returnFlights` arrays
3. Ensure all required fields are present
4. Use realistic pricing and timing
5. Include proper airline codes and logos

### Adding New Routes
1. Create new JSON files following the same structure
2. Update the `isInternationalRoute` function in `flightService.ts` if needed
3. Add new airport codes to the Indian airports list if required

### Airline Codes Used
- **6E**: IndiGo
- **AI**: Air India  
- **UK**: Vistara
- **SG**: SpiceJet
- **EK**: Emirates
- **QR**: Qatar Airways

## Testing

The system includes fallback mechanisms:
1. **Primary**: Real API data
2. **Secondary**: JSON dummy data (this directory)
3. **Tertiary**: Hardcoded mock data

You can see which data source is being used in development mode - look for the "Data: source-name" indicator in the search results header.

## Notes

- All prices are in Indian Rupees (INR)
- Dates use YYYY-MM-DD format
- Times use 24-hour format (HH:MM)
- Airport codes follow IATA standards
- Baggage allowances reflect real airline policies
- Flight durations are realistic for the routes
