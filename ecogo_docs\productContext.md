# EcoGO Flight Booking - Product Context

## Project Overview
EcoGO is a flight booking platform built with Next.js, React, and TypeScript. The application provides users with a comprehensive flight search and booking experience similar to major travel booking sites.

## Core Purpose
- Enable users to search for domestic and international flights
- Provide intuitive flight booking interface with modern UX
- Support both one-way and round-trip bookings
- Offer special fare categories (student, senior citizen, armed forces, medical professionals)
- Display competitive pricing with calendar-based price comparison

## Key Features
1. **Flight Search**: Comprehensive search with airport selection, date picking, and traveler/class selection
2. **Trip Types**: One-way and round-trip booking support
3. **Calendar Integration**: Price-aware date selection with lowest fare display
4. **Special Fares**: Multiple fare categories with discounts
5. **Responsive Design**: Mobile-first approach with desktop optimization
6. **Airport Selection**: Smart airport search with popular destinations

## Target Users
- Travelers seeking domestic and international flights
- Price-conscious customers looking for best deals
- Business and leisure travelers
- Special category travelers (students, seniors, armed forces, medical professionals)

## Business Value
- Competitive flight booking platform
- Enhanced user experience with modern interface
- Mobile-optimized for on-the-go bookings
- Special fare categories to capture diverse customer segments
