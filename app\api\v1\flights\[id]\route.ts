import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';
import * as fs from 'fs/promises';
import * as path from 'path';

// Create axios instance for backend API calls
const backendApi = axios.create({
  baseURL: 'http://192.168.205.163:8080',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds timeout
});

// Helper function to load flight details data from JSON file
async function loadFlightDetailsDummyData() {
  try {
    console.log('📁 Loading flight details data from flightdetails.json file...');

    const filePath = path.join(process.cwd(), 'public', 'assets', 'json', 'dummydata', 'flightdetails.json');
    const fileContents = await fs.readFile(filePath, 'utf8');
    const jsonData = JSON.parse(fileContents);

    console.log('✅ Successfully loaded flight details data from JSON file:', {
      success: jsonData.success,
      flightId: jsonData.data?.id,
      airline: jsonData.data?.airline,
      flightNumber: jsonData.data?.flightNumber
    });

    return jsonData;
  } catch (error) {
    console.error('❌ Error loading flightdetails.json:', error);

    // Return minimal fallback if JSON file can't be loaded
    return {
      success: false,
      message: 'Fallback data - JSON file not available',
      data: {
        id: 'fallback-flight',
        airline: '6E',
        logo: '/AirlineLogo/6E.png',
        flightNumber: '6E-XXXX',
        segments: [],
        departure: {
          time: '06:00',
          airport: 'DEL',
          city: 'Delhi',
          date: new Date().toISOString().split('T')[0]
        },
        arrival: {
          time: '09:00',
          airport: 'BOM',
          city: 'Mumbai',
          date: new Date().toISOString().split('T')[0]
        },
        duration: '3h 00m',
        stops: 'Non-stop',
        price: 5000,
        currency: 'INR',
        baggage: {
          cabin: '7 kg',
          checkin: '15 kg'
        },
        cancellation: 'Standard cancellation policy',
        features: [],
        fares: [],
        isRefundable: false,
        fareType: 'SAVER'
      }
    };
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const flightId = params.id;

  try {
    console.log('🚀 Flight Details API GET /v1/flights/{id} called with:', { 
      flightId
    });

    // Validate required parameters
    if (!flightId) {
      return NextResponse.json({
        success: false,
        message: "Flight ID parameter is required",
        data: null
      }, { status: 400 });
    }

    try {
      // Call backend API - GET /v1/flights/{id}
      const backendResponse = await backendApi.get(`/v1/flights/${flightId}`);

      console.log('✅ Backend API GET response received:', {
        status: backendResponse.status,
        hasData: !!backendResponse.data
      });

      // Return the backend response directly
      return NextResponse.json(backendResponse.data);

    } catch (backendError) {

      // Fallback: Load from JSON file
      const jsonData = await loadFlightDetailsDummyData();

      // Customize the dummy data with the requested flight ID
      if (jsonData.success && jsonData.data) {
        jsonData.data.id = flightId;
        jsonData.message = 'Flight details loaded from flightdetails.json file (API not available)';
      }

      const responseData = {
        ...jsonData,
        message: "Flight details loaded from flightdetails.json file (API not available)"
      };

      return NextResponse.json(responseData);
    }

  } catch (error) {

    // Even if there's a general error, try to load from JSON file
    try {
      console.log('🔄 Attempting to load from JSON file due to general error');
      const jsonData = await loadFlightDetailsDummyData();

      // Customize the dummy data with the requested flight ID
      if (jsonData.success && jsonData.data) {
        jsonData.data.id = flightId;
      }

      const responseData = {
        ...jsonData,
        message: "Flight details loaded from flightdetails.json file (API error occurred)"
      };

      return NextResponse.json(responseData);
    } catch (jsonError) {
      console.error('❌ Failed to load JSON fallback:', jsonError);

      return NextResponse.json({
        success: false,
        message: "Internal server error - no fallback data available",
        data: null
      }, { status: 500 });
    }
  }
}
