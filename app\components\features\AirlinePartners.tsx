
'use client';

export default function AirlinePartners() {
  

  return (
    <div>
      <div className="bg-gray-100 py-8 px-4 sm:px-6">
        <div className="max-w-7xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-6 md:p-8">
            <div className="flex flex-col lg:flex-row items-center justify-between">
              {/* Left Content */}
              <div className="flex items-center space-x-4 mb-6 lg:mb-0">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <i className="ri-smartphone-line text-white text-2xl"></i>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Download App Now !</h2>
                  <p className="text-gray-600">
                    Use code <span className="font-semibold text-blue-600">WELCOMEMMT</span> and get 
                    <span className="font-semibold text-green-600"> FLAT 12% OFF*</span> on your first domestic flight booking
                  </p>
                </div>
              </div>

              {/* Mobile Input & App Links */}
              <div className="flex flex-col space-y-4 lg:space-y-0 lg:space-x-6">
                {/* Phone Number Input */}
                {/* App Store Links */}
                <div className="flex items-center space-x-3">
                  <a href="#" className="block hover:scale-105 transition-transform">
                    <img
                      src="https://readdy.ai/api/search-image?query=Google%20Play%20Store%20download%20badge%20button%20black%20with%20white%20text%2C%20official%20Google%20Play%20logo%20design%2C%20mobile%20app%20download%20button&width=135&height=40&seq=google-play&orientation=landscape"
                      alt="Get it on Google Play"
                      className="h-10"
                    />
                  </a>
                  <a href="#" className="block hover:scale-105 transition-transform">
                    <img
                      src="https://readdy.ai/api/search-image?query=Download%20on%20the%20App%20Store%20button%20black%20with%20white%20Apple%20logo%2C%20official%20iOS%20app%20store%20badge%2C%20mobile%20app%20download&width=135&height=40&seq=app-store&orientation=landscape"
                      alt="Download on the App Store"
                      className="h-10"
                    />
                  </a>
                  
                  {/* QR Code */}
                  <div className="w-16 h-16 bg-white border-2 border-gray-300 rounded-lg flex items-center justify-center ml-4">
                    <img
                      src="https://readdy.ai/api/search-image?query=QR%20code%20black%20and%20white%20squares%20pattern%20for%20mobile%20app%20download%2C%20simple%20clean%20QR%20code%20design%20on%20white%20background&width=64&height=64&seq=qr-code&orientation=squarish"
                      alt="QR Code"
                      className="w-12 h-12"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Flight Destinations */}
      <div className="bg-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto ">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                city: 'Chennai Flights',
                routes: 'Via - Delhi, Mumbai, Coimbatore, Madurai',
                image: 'https://readdy.ai/api/search-image?query=Chennai%20Marina%20Beach%20coastline%20aerial%20view%20with%20lighthouse%20and%20blue%20ocean%2C%20South%20Indian%20coastal%20city%20landscape%2C%20travel%20destination%20photography&width=80&height=80&seq=chennai&orientation=squarish'
              },
              {
                city: 'Goa Flights',
                routes: 'Via - Delhi, Mumbai, Bangalore, Ahmedabad',
                image: 'https://readdy.ai/api/search-image?query=Goa%20beach%20with%20palm%20trees%20and%20golden%20sand%2C%20tropical%20paradise%20destination%2C%20Indian%20coastal%20tourism%2C%20azure%20blue%20waters&width=80&height=80&seq=goa&orientation=squarish'
              },
              {
                city: 'Mumbai Flights',
                routes: 'Via - Delhi, Bangalore, Chennai, Ahmedabad',
                image: 'https://readdy.ai/api/search-image?query=Mumbai%20skyline%20with%20skyscrapers%20and%20Marine%20Drive%20coastline%2C%20financial%20capital%20of%20India%2C%20urban%20cityscape%20at%20sunset&width=80&height=80&seq=mumbai&orientation=squarish'
              },
              {
                city: 'Hyderabad Flights',
                routes: 'Via - Chennai, Mumbai, Bangalore, Delhi',
                image: 'https://readdy.ai/api/search-image?query=Hyderabad%20Charminar%20historic%20monument%20with%20Islamic%20architecture%2C%20Telangana%20landmark%2C%20Indian%20heritage%20site%20photography&width=80&height=80&seq=hyderabad&orientation=squarish'
              },
              {
                city: 'Delhi Flights',
                routes: 'Via - Mumbai, Pune, Bangalore, Chennai',
                image: 'https://readdy.ai/api/search-image?query=Delhi%20India%20Gate%20monument%20with%20surrounding%20gardens%2C%20national%20capital%20landmark%2C%20historical%20architecture%20in%20warm%20lighting&width=80&height=80&seq=delhi&orientation=squarish'
              },
              {
                city: 'Pune Flights',
                routes: 'Via - Delhi, Bangalore, Chennai, Ahmedabad',
                image: 'https://readdy.ai/api/search-image?query=Pune%20city%20landscape%20with%20hills%20in%20background%2C%20IT%20hub%20of%20India%2C%20Maharashtra%20urban%20scenery%2C%20educational%20city%20view&width=80&height=80&seq=pune&orientation=squarish'
              },
              {
                city: 'Kolkata Flights',
                routes: 'Via - Delhi, Mumbai, Bangalore, Pune',
                image: 'https://readdy.ai/api/search-image?query=Kolkata%20Howrah%20Bridge%20over%20Hooghly%20river%20at%20dusk%2C%20West%20Bengal%20landmark%2C%20cultural%20capital%20of%20India%20architecture&width=80&height=80&seq=kolkata&orientation=squarish'
              },
              {
                city: 'Bangalore Flights',
                routes: 'Via - Delhi, Pune, Mumbai, Kolkata',
                image: 'https://readdy.ai/api/search-image?query=Bangalore%20IT%20park%20with%20modern%20glass%20buildings%2C%20Silicon%20Valley%20of%20India%2C%20Karnataka%20tech%20city%20skyline%2C%20garden%20city&width=80&height=80&seq=bangalore&orientation=squarish'
              },
              {
                city: 'Jaipur Flights',
                routes: 'Via - Mumbai, Delhi, Pune, Bangalore',
                image: 'https://readdy.ai/api/search-image?query=Jaipur%20Pink%20City%20palace%20architecture%20with%20traditional%20Rajasthani%20design%2C%20royal%20heritage%20buildings%2C%20Rajasthan%20tourism%20landmark&width=80&height=80&seq=jaipur&orientation=squarish'
              }
            ].map((destination, index) => (
              <div key={index} className="flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer">
                <div className="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                  <img
                    src={destination.image}
                    alt={destination.city}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-1">{destination.city}</h3>
                  <p className="text-sm text-gray-600">{destination.routes}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
