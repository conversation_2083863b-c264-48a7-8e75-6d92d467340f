/**
 * List Page Services
 * Handles all flight search, listing, filtering, and pagination functionality
 */

import api from './axiosinstance';
import { airportService, AirportSelectorData } from './airportService';

// ============================================================================
// INTERFACES AND TYPES
// ============================================================================

export interface FlightSearchFormData {
  from: AirportSelectorData;
  to: AirportSelectorData;
  departureDate: string;
  returnDate?: string;
  adults: number;
  children: number;
  infants: number;
  cabin: 'E' | 'PE' | 'B' | 'F'; // Economy, Premium Economy, Business, First
  tripType: 'oneWay' | 'roundTrip';
  fareType: 'ON' | 'RT' | 'MC'; // One Way, Round Trip, Multi City
  isDirect?: boolean;
}

export interface FlightSearchRequest {
  ClientID: string;
  Mode: string;
  Options: string;
  Source: string;
  TripType: string;
  Provider: string;
  Trips: Array<{
    Origin: string;
    Destination: string;
    DepartureDate: string;
  }>;
  Pax: {
    Adult: number;
    Child: number;
    Infant: number;
  };
  Cabin: string;
  FareType: string;
  IsDirect: boolean;
}

export interface Flight {
  id: string;
  airline: string;
  logo: string;
  flightNumber: string;
  segments: FlightSegment[];
  departure: {
    time: string;
    airport: string;
    city: string;
    date: string;
  };
  arrival: {
    time: string;
    airport: string;
    city: string;
    date: string;
  };
  duration: string;
  stops: string;
  price: number;
  currency: string;
  baggage: {
    cabin: string;
    checkin: string;
  };
  cancellation: string;
  rating: number;
  features: string[];
  fares: FlightFare[];
  isRefundable: boolean;
  fareType: string;
}

export interface FlightSegment {
  airline: string;
  flightNumber: string;
  departure: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  arrival: {
    airport: string;
    city: string;
    time: string;
    date: string;
  };
  duration: string;
  aircraft: string;
}

export interface FlightFare {
  type: string;
  price: number;
  currency: string;
  baggage: {
    cabin: string;
    checkin: string;
  };
  cancellation: string;
  features: string[];
}

export interface FlightSearchResponse {
  success: boolean;
  message: string;
  data?: {
    onwardFlights: Flight[];
    returnFlights?: Flight[];
    totalResults: number;
    tui?: string;
    searchId?: string;
  };
}

export interface SearchListResponse {
  TUI: string;
  Completed: string;
  CeilingInfo: string;
  TripType: string | null;
  ElapsedTime: string;
  Notices: string | null;
  Code?: string;
  Msg?: string[];
  Trips: SearchListTrip[];
}

export interface SearchListTrip {
  Journey: any[]; // Using any[] since the actual JSON structure is different
}

export interface SearchListJourney {
  Origin: string;
  Destination: string;
  DepartureDate: string;
  Segments: SearchListSegment[];
}

export interface SearchListSegment {
  Airline: string;
  FlightNumber: string;
  Origin: string;
  Destination: string;
  DepartureDateTime: string;
  ArrivalDateTime: string;
  Duration: string;
  Aircraft: string;
  Cabin: string;
  FareClass: string;
  Baggage: {
    Cabin: string;
    CheckIn: string;
  };
  Price: {
    Amount: number;
    Currency: string;
  };
  Taxes: {
    Amount: number;
    Currency: string;
  };
  TotalPrice: {
    Amount: number;
    Currency: string;
  };
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

export const isInternationalRoute = async (fromCode: string, toCode: string): Promise<boolean> => {
  try {
    const airports = await airportService.getAirports();
    const fromAirport = airports.find(airport => airport.code === fromCode);
    const toAirport = airports.find(airport => airport.code === toCode);

    if (!fromAirport || !toAirport) {

      // Fallback logic for common airport codes
      const indianAirports = ['DEL', 'BOM', 'BLR', 'MAA', 'CCU', 'HYD', 'AMD', 'PNQ', 'GOI', 'COK'];
      const isFromIndian = indianAirports.includes(fromCode);
      const isToIndian = indianAirports.includes(toCode);

      // If both are Indian airports, it's domestic
      if (isFromIndian && isToIndian) {
        return false;
      }

      // If one is Indian and other is not in the list, assume international
      if (isFromIndian || isToIndian) {
        return true;
      }

      // Default to domestic if both airports are unknown
      return false;
    }

    const isInternational = fromAirport.country !== toAirport.country;

    return isInternational;
  } catch (error) {

    // Fallback logic when airport service fails completely
    const indianAirports = ['DEL', 'BOM', 'BLR', 'MAA', 'CCU', 'HYD', 'AMD', 'PNQ', 'GOI', 'COK'];
    const isFromIndian = indianAirports.includes(fromCode);
    const isToIndian = indianAirports.includes(toCode);

    // If both are Indian airports, it's domestic
    if (isFromIndian && isToIndian) {
      return false;
    }

    // If one is Indian and other is not in the list, assume international
    if (isFromIndian || isToIndian) {
      return true;
    }

    // Default to domestic if both airports are unknown
    return false;
  }
};

export const transformToFlightSearchRequest = async (formData: FlightSearchFormData): Promise<FlightSearchRequest> => {
  const isInternational = await isInternationalRoute(formData.from.code, formData.to.code);

  return {
    ClientID: "ECOGO_WEB",
    Mode: isInternational ? "INT" : "DOM",
    Options: "50",
    Source: "WEB",
    TripType: formData.tripType === 'roundTrip' ? 'RT' : 'OW',
    Provider: "ALL",
    Trips: [
      {
        Origin: formData.from.code,
        Destination: formData.to.code,
        DepartureDate: formData.departureDate
      },
      ...(formData.tripType === 'roundTrip' && formData.returnDate ? [{
        Origin: formData.to.code,
        Destination: formData.from.code,
        DepartureDate: formData.returnDate
      }] : [])
    ],
    Pax: {
      Adult: formData.adults,
      Child: formData.children,
      Infant: formData.infants
    },
    Cabin: formData.cabin,
    FareType: formData.fareType,
    IsDirect: formData.isDirect || false
  };
};

// Transform search list response to flights with proper round-trip handling
export const transformSearchListResponseToFlights = (searchListResponse: any): Flight[] => {
  if (!searchListResponse.Trips || searchListResponse.Trips.length === 0) {
    return [];
  }

  const flights: Flight[] = [];

  searchListResponse.Trips.forEach((trip: any, tripIndex: number) => {
    if (trip.Journey && Array.isArray(trip.Journey)) {
      trip.Journey.forEach((journey: any, journeyIndex: number) => {
        // Handle the actual JSON structure from searchlistdummy.json
        const flight: Flight = {
          id: `${searchListResponse.TUI}-${tripIndex}-${journeyIndex}`,
          airline: journey.VAC || journey.MAC || journey.OAC || 'AI',
          logo: `/AirlineLogo/${journey.VAC || journey.MAC || journey.OAC || 'AI'}.png`,
          flightNumber: journey.FlightNo || 'Unknown',
          segments: [{
            airline: journey.VAC || journey.MAC || journey.OAC || 'AI',
            flightNumber: journey.FlightNo || 'Unknown',
            departure: {
              airport: journey.From || 'DEL',
              city: journey.FromName ? journey.FromName.split('|')[1] || journey.From : journey.From || 'Delhi',
              time: journey.DepartureTime ? new Date(journey.DepartureTime).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              }) : '06:00',
              date: journey.DepartureTime ? new Date(journey.DepartureTime).toISOString().split('T')[0] : '2025-08-21'
            },
            arrival: {
              airport: journey.To || 'BOM',
              city: journey.ToName ? journey.ToName.split('|')[1] || journey.To : journey.To || 'Mumbai',
              time: journey.ArrivalTime ? new Date(journey.ArrivalTime).toLocaleTimeString('en-US', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
              }) : '08:30',
              date: journey.ArrivalTime ? new Date(journey.ArrivalTime).toISOString().split('T')[0] : '2025-08-21'
            },
            duration: journey.Duration || '2h 30m',
            aircraft: journey.AirCraft || 'Boeing 737'
          }],
          departure: {
            time: journey.DepartureTime ? new Date(journey.DepartureTime).toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            }) : '06:00',
            airport: journey.From || 'DEL',
            city: journey.FromName ? journey.FromName.split('|')[1] || journey.From : journey.From || 'Delhi',
            date: journey.DepartureTime ? new Date(journey.DepartureTime).toISOString().split('T')[0] : '2025-08-21'
          },
          arrival: {
            time: journey.ArrivalTime ? new Date(journey.ArrivalTime).toLocaleTimeString('en-US', {
              hour: '2-digit',
              minute: '2-digit',
              hour12: false
            }) : '08:30',
            airport: journey.To || 'BOM',
            city: journey.ToName ? journey.ToName.split('|')[1] || journey.To : journey.To || 'Mumbai',
            date: journey.ArrivalTime ? new Date(journey.ArrivalTime).toISOString().split('T')[0] : '2025-08-21'
          },
          duration: journey.Duration || '2h 30m',
          stops: journey.Stops ? journey.Stops.toString() : '0',
          price: journey.NetFare || journey.GrossFare || 5500,
          currency: 'INR',
          baggage: {
            cabin: '7 kg',
            checkin: '15 kg'
          },
          cancellation: journey.Refundable === 'R' ? 'Refundable' : 'Non-refundable',
          rating: 4.2,
          features: ['Meal', 'Entertainment'],
          fares: [{
            type: journey.FareClass || 'Economy',
            price: journey.NetFare || journey.GrossFare || 5500,
            currency: 'INR',
            baggage: {
              cabin: '7 kg',
              checkin: '15 kg'
            },
            cancellation: journey.Refundable === 'R' ? 'Refundable' : 'Non-refundable',
            features: ['Meal', 'Entertainment']
          }],
          isRefundable: journey.Refundable === 'R',
          fareType: journey.Cabin || 'E'
        };

        flights.push(flight);
      });
    }
  });

  return flights;
};

// Separate flights by journey direction for round-trip
export const separateFlightsByJourney = (flights: Flight[], fromCode: string, toCode: string): { onwardFlights: Flight[], returnFlights: Flight[] } => {
  const onwardFlights = flights.filter(flight =>
    flight.departure.airport === fromCode && flight.arrival.airport === toCode
  );

  const returnFlights = flights.filter(flight =>
    flight.departure.airport === toCode && flight.arrival.airport === fromCode
  );

  return { onwardFlights, returnFlights };
};

// Load dummy search list data from JSON file
export const loadDummySearchListData = async (): Promise<SearchListResponse> => {
  try {
    const response = await fetch('/assets/json/dummydata/searchlistdummy.json');
    if (!response.ok) {
      throw new Error(`Failed to load JSON file: ${response.status}`);
    }

    const data = await response.json();

    return data;
  } catch (error) {

    // Return fallback data
    return {
      TUI: 'FALLBACK_TUI',
      Completed: 'True',
      CeilingInfo: '',
      TripType: 'OW',
      ElapsedTime: '0',
      Notices: null,
      Trips: []
    };
  }
};

// Load round trip data from JSON files
export const loadRoundTripData = async (fromCode: string, toCode: string) => {
  try {

    // Load search list data
    const searchListData = await loadDummySearchListData();
    const searchListFlights = transformSearchListResponseToFlights(searchListData);

    // Use the proper separation function for round trip
    const { onwardFlights, returnFlights } = separateFlightsByJourney(searchListFlights, fromCode, toCode);

    // If no flights found by direction, fall back to splitting by half
    let finalOnwardFlights = onwardFlights;
    let finalReturnFlights = returnFlights;

    if (onwardFlights.length === 0 && returnFlights.length === 0) {
      const midPoint = Math.ceil(searchListFlights.length / 2);
      finalOnwardFlights = searchListFlights.slice(0, midPoint);
      finalReturnFlights = searchListFlights.slice(midPoint);
    }

    return {
      onwardFlights: finalOnwardFlights,
      returnFlights: finalReturnFlights
    };
  } catch (error) {
    throw error;
  }
};

// ============================================================================
// MAIN LIST PAGE SERVICE
// ============================================================================

export const listPageService = {
  // Search flights with form data
  async searchFlights(formData: FlightSearchFormData): Promise<FlightSearchResponse> {
    try {
      const searchRequest = await transformToFlightSearchRequest(formData);

      const response = await api.post('/search/', searchRequest);

      if (response.data) {

        // Handle different API response structures
        let onwardFlights = response.data.onwardFlights || [];
        let returnFlights = response.data.returnFlights || [];

        // If API returns all flights in a single array, separate them for round-trip
        if (formData.tripType === 'roundTrip' && response.data.flights && !response.data.onwardFlights) {
          const separated = separateFlightsByJourney(response.data.flights, formData.from.code, formData.to.code);
          onwardFlights = separated.onwardFlights;
          returnFlights = separated.returnFlights;
        }

        return {
          success: true,
          message: 'Flights found successfully',
          data: {
            onwardFlights,
            returnFlights,
            totalResults: onwardFlights.length + returnFlights.length,
            tui: response.data.tui,
            searchId: response.data.searchId
          }
        };
      } else {
        throw new Error('No data received from search API');
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Flight search failed'
      };
    }
  },

  // Search flights with search-list integration
  async searchFlightsWithFormDataAndSearchList(formData: FlightSearchFormData): Promise<{
    searchResponse: FlightSearchResponse;
    searchListResponse?: SearchListResponse;
  }> {
    try {
      // First, try the main search
      const searchResponse = await this.searchFlights(formData);

      // If main search fails or returns no results, try search-list
      if (!searchResponse.success || !searchResponse.data?.onwardFlights?.length) {

        try {
          const searchListData = await loadDummySearchListData();
          const searchListFlights = transformSearchListResponseToFlights(searchListData);

          if (searchListFlights.length > 0) {

            // Properly separate onward and return flights for round-trip
            let onwardFlights = searchListFlights;
            let returnFlights: Flight[] = [];

            if (formData.tripType === 'roundTrip') {
              // Use the new separation function to properly split flights by journey direction
              const separated = separateFlightsByJourney(searchListFlights, formData.from.code, formData.to.code);
              onwardFlights = separated.onwardFlights;
              returnFlights = separated.returnFlights;

              // If no flights found in proper direction, fall back to splitting by half
              if (onwardFlights.length === 0 && returnFlights.length === 0) {
                const midPoint = Math.ceil(searchListFlights.length / 2);
                onwardFlights = searchListFlights.slice(0, midPoint);
                returnFlights = searchListFlights.slice(midPoint);
              }
            }

            return {
              searchResponse: {
                success: true,
                message: 'Flights loaded from search-list data',
                data: {
                  onwardFlights,
                  returnFlights,
                  totalResults: onwardFlights.length + returnFlights.length,
                  tui: searchListData.TUI
                }
              },
              searchListResponse: searchListData
            };
          }
        } catch (searchListError) {
        }
      }

      return { searchResponse };
    } catch (error: any) {
      return {
        searchResponse: {
          success: false,
          message: error.message || 'Combined search failed'
        }
      };
    }
  },

  // Get search list data
  async getSearchListData(tui?: string): Promise<{ success: boolean; data?: SearchListResponse; message: string }> {
    try {

      if (tui) {
        // Try to get specific TUI data from API
        try {
          const response = await api.get(`/search-list/${tui}`);
          if (response.data) {
            return {
              success: true,
              data: response.data,
              message: 'Search-list data retrieved successfully'
            };
          }
        } catch (apiError) {
        }
      }

      // Fallback to JSON data
      const jsonData = await loadDummySearchListData();
      return {
        success: true,
        data: jsonData,
        message: 'Search-list data loaded from JSON file'
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || 'Failed to get search-list data'
      };
    }
  },

  // Transform search list to flights
  transformSearchListToFlights: transformSearchListResponseToFlights,

  // Separate flights by journey direction
  separateFlightsByJourney,

  // Load round trip data
  loadRoundTripData,

  // Check if route is international
  isInternationalRoute
};