'use client';

import React, { useState, useEffect } from 'react';
import { airportService } from '@/app/api/services/airportService';
import { listPageService, FlightSearchFormData } from '@/app/api/services/list-page-services';
import { previewPageService } from '@/app/api/services/preview-page-service';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error' | 'fallback';
  message: string;
  data?: any;
  duration?: number;
}

export default function ApiFlowTest() {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string>('');

  const updateTestResult = (name: string, status: TestResult['status'], message: string, data?: any, duration?: number) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.name === name);
      if (existing) {
        existing.status = status;
        existing.message = message;
        existing.data = data;
        existing.duration = duration;
        return [...prev];
      } else {
        return [...prev, { name, status, message, data, duration }];
      }
    });
  };

  const runTest = async (testName: string, testFn: () => Promise<any>) => {
    setCurrentTest(testName);
    updateTestResult(testName, 'pending', 'Running...');
    
    const startTime = Date.now();
    try {
      const result = await testFn();
      const duration = Date.now() - startTime;
      
      if (result.fallback) {
        updateTestResult(testName, 'fallback', result.message || 'Using fallback data', result.data, duration);
      } else {
        updateTestResult(testName, 'success', result.message || 'Success', result.data, duration);
      }
    } catch (error: any) {
      const duration = Date.now() - startTime;
      updateTestResult(testName, 'error', error.message || 'Test failed', null, duration);
    }
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    setCurrentTest('');

    // Test 1: API Connectivity Check
    await runTest('API Connectivity', async () => {
      const isConnected = await airportService.checkApiConnectivity();
      return {
        message: isConnected ? 'API is accessible' : 'API is not accessible',
        data: { connected: isConnected },
        fallback: !isConnected
      };
    });

    // Test 2: Airport Service - Get Airports
    await runTest('Airport Service - Get Airports', async () => {
      const airports = await airportService.getAirports();
      const isFallback = airports.length <= 20; // Fallback data has ~20 airports
      return {
        message: isFallback ? 'Using fallback airport data' : 'Loaded airports from API',
        data: { count: airports.length, sample: airports.slice(0, 3) },
        fallback: isFallback
      };
    });

    // Test 3: Airport Service - Search Airports
    await runTest('Airport Service - Search Airports', async () => {
      const searchResults = await airportService.searchAirports('Delhi');
      const isFallback = searchResults.length > 0 && searchResults[0].code === 'DEL';
      return {
        message: isFallback ? 'Using local search in fallback data' : 'API search successful',
        data: { count: searchResults.length, results: searchResults },
        fallback: isFallback
      };
    });

    // Test 4: Route Type Detection
    await runTest('Route Type Detection - Domestic', async () => {
      const isDomestic = !(await listPageService.isInternationalRoute('DEL', 'BOM'));
      return {
        message: isDomestic ? 'Correctly identified as domestic' : 'Incorrectly identified as international',
        data: { route: 'DEL → BOM', isDomestic }
      };
    });

    await runTest('Route Type Detection - International', async () => {
      const isInternational = await listPageService.isInternationalRoute('DEL', 'DXB');
      return {
        message: isInternational ? 'Correctly identified as international' : 'Incorrectly identified as domestic',
        data: { route: 'DEL → DXB', isInternational }
      };
    });

    // Test 5: Flight Search - One Way
    await runTest('Flight Search - One Way', async () => {
      const formData: FlightSearchFormData = {
        from: { code: 'DEL', name: 'Delhi', fullName: 'Delhi Airport', country: 'India' },
        to: { code: 'BOM', name: 'Mumbai', fullName: 'Mumbai Airport', country: 'India' },
        departureDate: '2025-08-21',
        adults: 1,
        children: 0,
        infants: 0,
        cabin: 'E',
        tripType: 'oneWay',
        fareType: 'ON'
      };

      const result = await listPageService.searchFlightsWithFormDataAndSearchList(formData);
      const isFallback = !!result.searchListResponse;
      
      return {
        message: isFallback ? 'Using search-list fallback data' : 'API search successful',
        data: {
          success: result.searchResponse.success,
          onwardFlights: result.searchResponse.data?.onwardFlights?.length || 0,
          returnFlights: result.searchResponse.data?.returnFlights?.length || 0
        },
        fallback: isFallback
      };
    });

    // Test 6: Flight Search - Round Trip
    await runTest('Flight Search - Round Trip', async () => {
      const formData: FlightSearchFormData = {
        from: { code: 'DEL', name: 'Delhi', fullName: 'Delhi Airport', country: 'India' },
        to: { code: 'BOM', name: 'Mumbai', fullName: 'Mumbai Airport', country: 'India' },
        departureDate: '2025-08-21',
        returnDate: '2025-08-25',
        adults: 1,
        children: 0,
        infants: 0,
        cabin: 'E',
        tripType: 'roundTrip',
        fareType: 'RT'
      };

      const result = await listPageService.searchFlightsWithFormDataAndSearchList(formData);
      const isFallback = !!result.searchListResponse;
      
      return {
        message: isFallback ? 'Using search-list fallback data with proper separation' : 'API search successful',
        data: {
          success: result.searchResponse.success,
          onwardFlights: result.searchResponse.data?.onwardFlights?.length || 0,
          returnFlights: result.searchResponse.data?.returnFlights?.length || 0,
          properSeparation: (result.searchResponse.data?.onwardFlights?.length || 0) > 0 && 
                           (result.searchResponse.data?.returnFlights?.length || 0) > 0
        },
        fallback: isFallback
      };
    });

    // Test 7: Load Round Trip Data
    await runTest('Load Round Trip Data', async () => {
      const roundTripData = await listPageService.loadRoundTripData('DEL', 'BOM');
      
      return {
        message: 'Round trip data loaded with proper separation',
        data: {
          onwardFlights: roundTripData.onwardFlights.length,
          returnFlights: roundTripData.returnFlights.length,
          properSeparation: roundTripData.onwardFlights.length > 0 && roundTripData.returnFlights.length > 0
        },
        fallback: true // This always uses JSON data
      };
    });

    // Test 8: Preview Service - Get Preview Data
    await runTest('Preview Service - Get Preview Data', async () => {
      const previewResult = await previewPageService.getPreviewData('TEST_TUI', 'DEL', 'BOM');
      const isFallback = previewResult.message?.includes('JSON file');
      
      return {
        message: isFallback ? 'Using JSON fallback preview data' : 'API preview data loaded',
        data: {
          success: previewResult.success,
          hasData: !!previewResult.data,
          totalAmount: previewResult.data?.TotalAmount
        },
        fallback: isFallback
      };
    });

    // Test 9: Preview Service - Fare Breakdown
    await runTest('Preview Service - Fare Breakdown', async () => {
      const fareBreakdown = await previewPageService.getFareBreakdown('TEST_TUI', {
        adults: 1,
        children: 0,
        infants: 0
      });
      
      return {
        message: 'Fare breakdown calculated successfully',
        data: {
          baseFare: fareBreakdown.BaseFare,
          taxes: fareBreakdown.Taxes,
          totalAmount: fareBreakdown.TotalAmount,
          currency: fareBreakdown.Currency
        }
      };
    });

    // Test 10: Preview Service - Payment Options
    await runTest('Preview Service - Payment Options', async () => {
      const paymentOptions = await previewPageService.getPaymentOptions('TEST_TUI');

      return {
        message: 'Payment options loaded successfully',
        data: {
          count: paymentOptions.length,
          options: paymentOptions.map(opt => opt.Name)
        }
      };
    });

    // Test 11: JSON Fallback - Search List Data
    await runTest('JSON Fallback - Search List Data', async () => {
      const searchListResult = await listPageService.getSearchListData();

      return {
        message: 'Search list JSON data loaded successfully',
        data: {
          success: searchListResult.success,
          tripsCount: searchListResult.data?.Trips?.length || 0,
          firstTripJourneyCount: searchListResult.data?.Trips?.[0]?.Journey?.length || 0,
          sampleJourney: searchListResult.data?.Trips?.[0]?.Journey?.[0] || null
        },
        fallback: true
      };
    });

    // Test 12: JSON Fallback - Flight Transformation
    await runTest('JSON Fallback - Flight Transformation', async () => {
      const searchListResult = await listPageService.getSearchListData();
      if (searchListResult.success && searchListResult.data) {
        const flights = listPageService.transformSearchListToFlights(searchListResult.data);

        return {
          message: `Successfully transformed ${flights.length} flights from JSON data`,
          data: {
            flightsCount: flights.length,
            sampleFlight: flights[0] ? {
              id: flights[0].id,
              airline: flights[0].airline,
              flightNumber: flights[0].flightNumber,
              price: flights[0].price,
              departure: flights[0].departure,
              arrival: flights[0].arrival
            } : null
          },
          fallback: true
        };
      } else {
        throw new Error('No search list data available for transformation');
      }
    });

    setIsRunning(false);
    setCurrentTest('');
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending': return '⏳';
      case 'success': return '✅';
      case 'error': return '❌';
      case 'fallback': return '🔄';
      default: return '⚪';
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending': return 'text-yellow-600';
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      case 'fallback': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-6">
          🧪 API Flow & Fallback Test Suite
        </h1>
        
        <div className="mb-6">
          <button
            onClick={runAllTests}
            disabled={isRunning}
            className={`px-6 py-3 rounded-lg font-semibold ${
              isRunning
                ? 'bg-gray-400 cursor-not-allowed'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {isRunning ? '🔄 Running Tests...' : '🚀 Run All Tests'}
          </button>
          
          {currentTest && (
            <div className="mt-2 text-sm text-gray-600">
              Currently running: <span className="font-semibold">{currentTest}</span>
            </div>
          )}
        </div>

        <div className="space-y-4">
          {testResults.map((result, index) => (
            <div
              key={index}
              className="border rounded-lg p-4 bg-gray-50"
            >
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{getStatusIcon(result.status)}</span>
                  <h3 className="font-semibold text-lg">{result.name}</h3>
                  {result.duration && (
                    <span className="text-sm text-gray-500">
                      ({result.duration}ms)
                    </span>
                  )}
                </div>
                <span className={`font-medium ${getStatusColor(result.status)}`}>
                  {result.status.toUpperCase()}
                </span>
              </div>
              
              <p className={`text-sm mb-2 ${getStatusColor(result.status)}`}>
                {result.message}
              </p>
              
              {result.data && (
                <details className="text-xs">
                  <summary className="cursor-pointer text-gray-600 hover:text-gray-800">
                    View Details
                  </summary>
                  <pre className="mt-2 p-2 bg-gray-100 rounded overflow-x-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
            </div>
          ))}
        </div>

        {testResults.length > 0 && (
          <div className="mt-6 p-4 bg-gray-100 rounded-lg">
            <h3 className="font-semibold mb-2">Test Summary</h3>
            <div className="grid grid-cols-4 gap-4 text-sm">
              <div className="text-center">
                <div className="text-2xl">✅</div>
                <div>Success: {testResults.filter(r => r.status === 'success').length}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl">🔄</div>
                <div>Fallback: {testResults.filter(r => r.status === 'fallback').length}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl">❌</div>
                <div>Error: {testResults.filter(r => r.status === 'error').length}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl">⏳</div>
                <div>Pending: {testResults.filter(r => r.status === 'pending').length}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
